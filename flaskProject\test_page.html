<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能教案生成器测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #409eff;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        .objectives-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            min-height: 60px;
            background-color: #f9f9f9;
        }
        .objective-tag {
            display: inline-block;
            background-color: #409eff;
            color: white;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            font-size: 12px;
        }
        .btn {
            background-color: #409eff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            background-color: #66b1ff;
        }
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .btn-success {
            background-color: #67c23a;
        }
        .btn-success:hover {
            background-color: #85ce61;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background-color: #f0f9ff;
            border: 1px solid #409eff;
            color: #409eff;
        }
        .result.error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .loading {
            text-align: center;
            color: #409eff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 智能教案生成器</h1>
        
        <form id="lessonForm">
            <div class="form-group">
                <label for="subject">学科 *</label>
                <select id="subject" required>
                    <option value="">请选择学科</option>
                    <option value="语文">语文</option>
                    <option value="数学">数学</option>
                    <option value="英语">英语</option>
                    <option value="物理">物理</option>
                    <option value="化学">化学</option>
                    <option value="生物">生物</option>
                    <option value="历史">历史</option>
                    <option value="地理">地理</option>
                    <option value="政治">政治</option>
                </select>
            </div>

            <div class="form-group">
                <label for="grade">年级 *</label>
                <select id="grade" required>
                    <option value="">请选择年级</option>
                    <option value="一年级">一年级</option>
                    <option value="二年级">二年级</option>
                    <option value="三年级">三年级</option>
                    <option value="四年级">四年级</option>
                    <option value="五年级">五年级</option>
                    <option value="六年级">六年级</option>
                    <option value="七年级">七年级</option>
                    <option value="八年级">八年级</option>
                    <option value="九年级">九年级</option>
                </select>
            </div>

            <div class="form-group">
                <label for="topic">课题 *</label>
                <input type="text" id="topic" placeholder="请输入课题名称" required>
            </div>

            <div class="form-group">
                <label for="duration">课时长度（分钟）</label>
                <input type="number" id="duration" value="45" min="10" max="120">
            </div>

            <div class="form-group">
                <label>教学目标 *</label>
                <input type="text" id="objectiveInput" placeholder="输入教学目标后按回车添加">
                <div class="objectives-container" id="objectivesContainer">
                    <span style="color: #999;">请添加至少一个教学目标</span>
                </div>
            </div>

            <div class="form-group">
                <label for="keyPoints">重点难点</label>
                <textarea id="keyPoints" placeholder="请描述教学重点和难点"></textarea>
            </div>

            <div class="form-group">
                <label for="materials">教学材料</label>
                <textarea id="materials" placeholder="请描述所需的教学材料和工具"></textarea>
            </div>

            <div class="form-group">
                <label for="homework">作业布置</label>
                <textarea id="homework" placeholder="请描述课后作业安排"></textarea>
            </div>

            <div style="text-align: center;">
                <button type="button" class="btn" onclick="resetForm()">重置表单</button>
                <button type="submit" class="btn btn-success" id="generateBtn">生成Word教案</button>
            </div>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        let objectives = [];

        // 添加教学目标
        document.getElementById('objectiveInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const value = this.value.trim();
                if (value && !objectives.includes(value)) {
                    objectives.push(value);
                    updateObjectivesDisplay();
                    this.value = '';
                }
            }
        });

        function updateObjectivesDisplay() {
            const container = document.getElementById('objectivesContainer');
            if (objectives.length === 0) {
                container.innerHTML = '<span style="color: #999;">请添加至少一个教学目标</span>';
            } else {
                container.innerHTML = objectives.map(obj => 
                    `<span class="objective-tag">${obj} <span onclick="removeObjective('${obj}')" style="cursor: pointer;">×</span></span>`
                ).join('');
            }
        }

        function removeObjective(objective) {
            objectives = objectives.filter(obj => obj !== objective);
            updateObjectivesDisplay();
        }

        function resetForm() {
            document.getElementById('lessonForm').reset();
            objectives = [];
            updateObjectivesDisplay();
            hideResult();
        }

        function showResult(message, isSuccess = true) {
            const result = document.getElementById('result');
            result.className = `result ${isSuccess ? 'success' : 'error'}`;
            result.innerHTML = message;
            result.style.display = 'block';
        }

        function hideResult() {
            document.getElementById('result').style.display = 'none';
        }

        // 表单提交
        document.getElementById('lessonForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // 验证必填字段
            const subject = document.getElementById('subject').value;
            const grade = document.getElementById('grade').value;
            const topic = document.getElementById('topic').value;
            
            if (!subject || !grade || !topic) {
                showResult('请填写学科、年级和课题', false);
                return;
            }
            
            if (objectives.length === 0) {
                showResult('请至少添加一个教学目标', false);
                return;
            }

            // 准备数据
            const formData = {
                subject: subject,
                grade: grade,
                topic: topic,
                duration: parseInt(document.getElementById('duration').value) || 45,
                objectives: objectives,
                keyPoints: document.getElementById('keyPoints').value,
                materials: document.getElementById('materials').value,
                methods: ['lecture', 'discussion', 'practice'], // 默认教学方法
                steps: [
                    {
                        title: '导入新课',
                        content: '引入课题，激发学习兴趣',
                        time: 10
                    },
                    {
                        title: '新课讲解',
                        content: '详细讲解课程内容',
                        time: 25
                    },
                    {
                        title: '练习巩固',
                        content: '学生练习，巩固知识',
                        time: 10
                    }
                ],
                homework: document.getElementById('homework').value,
                reflection: '待课后补充'
            };

            // 显示加载状态
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = true;
            generateBtn.textContent = '生成中...';
            showResult('<div class="loading">🔄 正在生成教案，请稍候...</div>');

            try {
                // 发送请求
                const response = await fetch('http://localhost:5300/api/lesson-plan/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.code === 0) {
                    const filename = result.data.filename;
                    showResult(`✅ 教案生成成功！<br><br><a href="http://localhost:5300/api/lesson-plan/download/${filename}" download style="color: #409eff; text-decoration: none;">📄 点击下载Word文档</a>`);
                } else {
                    showResult(`❌ 教案生成失败：${result.message}`, false);
                }
            } catch (error) {
                showResult(`❌ 网络错误：${error.message}`, false);
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '生成Word教案';
            }
        });
    </script>
</body>
</html>
