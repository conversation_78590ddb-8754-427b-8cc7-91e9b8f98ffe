<template>
  <div class="simple-ppt-create">
    <div class="form-group">
      <label for="title">PPT标题 <span class="required">*</span></label>
      <input
        id="title"
        type="text"
        v-model="pptOptions.title"
        placeholder="请输入PPT标题"
      />
    </div>

    <div class="form-group">
      <label for="content">PPT内容描述 <span class="required">*</span></label>
      <textarea
        id="content"
        v-model="inputContent"
        placeholder="请详细描述您需要的PPT内容，包括主题、要点和特殊要求..."
        rows="5"
      ></textarea>
      <div class="input-tip">详细的描述将帮助AI生成更符合您需求的PPT，内容越详细，生成的PPT质量越高</div>
    </div>

    <div class="form-row">
      <div class="form-group half">
        <label for="slide_count">幻灯片数量</label>
        <input
          id="slide_count"
          type="number"
          v-model="pptOptions.slide_count"
          min="10"
          max="30"
          placeholder="建议15-20张"
        />
      </div>

      <div class="form-group half">
        <label for="font_family">字体选择</label>
        <select id="font_family" v-model="pptOptions.font_family">
          <option value="微软雅黑">微软雅黑</option>
          <option value="宋体">宋体</option>
          <option value="黑体">黑体</option>
          <option value="楷体">楷体</option>
          <option value="Arial">Arial</option>
        </select>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group half">
        <label for="template_style">模板风格</label>
        <select id="template_style" v-model="pptOptions.template_style">
          <option value="professional">专业商务</option>
          <option value="creative">创意设计</option>
          <option value="minimal">简约风格</option>
          <option value="academic">学术报告</option>
          <option value="modern">现代科技</option>
          <option value="elegant">优雅经典</option>
        </select>
      </div>

      <div class="form-group half">
        <label for="color_theme">颜色主题</label>
        <select id="color_theme" v-model="pptOptions.color_theme">
          <option value="blue">蓝色</option>
          <option value="green">绿色</option>
          <option value="red">红色</option>
          <option value="purple">紫色</option>
          <option value="orange">橙色</option>
          <option value="teal">青色</option>
          <option value="gray">灰色</option>
        </select>
      </div>
    </div>

    <div class="form-actions">
      <button
        @click="generatePPT"
        :disabled="isLoading"
        class="generate-btn"
      >
        <span v-if="isLoading" class="loading-spinner"></span>
        {{ isLoading ? '正在生成PPT...' : '生成PPT' }}
      </button>
    </div>

    <div v-if="errorMessage" class="error-message">
      <div class="error-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
      </div>
      <div class="error-text">{{ errorMessage }}</div>
    </div>

    <!-- 成功消息 -->
    <div v-if="downloadUrl" class="success-message">
      <div class="success-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
      </div>
      <div class="success-text">
        <div>PPT生成成功！文件已开始下载</div>
        <div class="download-again" @click="downloadPPT(downloadUrl)">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
          <span>再次下载</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimplePPTCreate',
  props: {
    token: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      inputContent: '',
      isLoading: false,
      errorMessage: '',
      downloadUrl: '',
      pptOptions: {
        title: '',
        slide_count: 15,
        template_style: 'professional',
        color_theme: 'blue',
        font_family: '微软雅黑',
      }
    }
  },
  methods: {
    async generatePPT() {
      if (this.inputContent.trim() === '') {
        this.errorMessage = '请输入PPT内容描述';
        return;
      }
      this.isLoading = true;
      this.errorMessage = '';
      this.downloadUrl = '';

      try {
        // 准备表单数据
        const formData = new FormData();
        formData.append('content', this.inputContent.trim());
        formData.append('title', this.pptOptions.title || '');
        formData.append('slide_count', this.pptOptions.slide_count || '15');
        formData.append('template_style', this.pptOptions.template_style || 'professional');
        formData.append('color_theme', this.pptOptions.color_theme || 'blue');
        formData.append('font_family', this.pptOptions.font_family || '微软雅黑');
        formData.append('token', this.token);
        formData.append('version', 'simple');

        const response = await fetch('http://localhost:5300/api/ppt/simple', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          // 直接处理文件响应
          const blob = await response.blob();

          // 从响应头获取文件名，或使用默认文件名
          const filename = this.getFilenameFromResponse(response) || `${this.pptOptions.title || 'presentation'}.pptx`;

          // 创建一个临时URL
          const url = window.URL.createObjectURL(blob);
          this.downloadUrl = url;

          // 触发下载
          this.downloadPPT(url, filename);

          // 显示成功消息
          this.$nextTick(() => {
            this.errorMessage = '';
          });
        } else {
          // 尝试解析错误响应
          try {
            const error = await response.json();
            this.errorMessage = '生成失败: ' + (error.error || '未知错误');
          } catch (e) {
            this.errorMessage = `生成失败: HTTP错误 ${response.status}`;
          }
          console.error('生成PPT失败:', response.status);
        }
      } catch (error) {
        console.error('生成PPT出错:', error);
        if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
          this.errorMessage = '无法连接到服务器，请检查网络连接或确保后端服务正在运行';
        } else if (error.name === 'AbortError') {
          this.errorMessage = '请求超时，请稍后重试';
        } else {
          this.errorMessage = '生成PPT时出错，请稍后重试';
        }
      } finally {
        this.isLoading = false;
      }
    },

    // 从响应头中获取文件名
    getFilenameFromResponse(response) {
      const contentDisposition = response.headers.get('content-disposition');
      if (!contentDisposition) return null;

      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        return filenameMatch[1].replace(/['"]/g, '');
      }
      return null;
    },



    // 下载PPT文件
    downloadPPT(url, filename) {
      // 创建一个隐藏的a标签来触发下载
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename || `${this.pptOptions.title || 'presentation'}.pptx`;
      document.body.appendChild(a);
      a.click();

      // 下载开始后移除元素
      setTimeout(() => {
        document.body.removeChild(a);

        // 如果是Blob URL，需要释放
        if (url.startsWith('blob:')) {
          window.URL.revokeObjectURL(url);
        }
      }, 100);

      // 显示成功消息
      this.$nextTick(() => {
        this.errorMessage = '';
        // 设置下载URL以显示成功消息
        if (!this.downloadUrl) {
          this.downloadUrl = url;
        }
      });
    }
  }
}
</script>

<style scoped>
@import '@/assets/css/form-styles.css';

.simple-ppt-create {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 调整表单行的样式，使其在横版布局中更美观 */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group.half {
  flex: 1;
  min-width: 0; /* 防止flex子项溢出 */
}

/* 调整文本区域的高度 */
textarea {
  min-height: 150px;
}

/* 调整按钮位置 */
.form-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.success-message {
  display: flex;
  align-items: center;
  margin-top: 20px;
  padding: 12px 15px;
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 8px;
  font-size: 14px;
  border-left: 3px solid #4caf50;
}

.success-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #4caf50;
}

.success-text {
  flex: 1;
}

.download-again {
  display: inline-flex;
  align-items: center;
  margin-top: 5px;
  color: #42b983;
  cursor: pointer;
  font-size: 13px;
}

.download-again svg {
  margin-right: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 10px;
  }

  .form-group.half {
    width: 100%;
  }
}
</style>
