#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import json
import requests

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("测试DeepSeek API集成的教案生成功能...")

# 测试数据
test_data = {
    "subject": "语文",
    "grade": "六年级",
    "topic": "《草原》课文分析",
    "duration": 45,
    "objectives": [
        "理解课文内容，感受草原的美丽风光",
        "学习作者的写作手法，体会语言的生动性",
        "培养学生热爱大自然的情感"
    ],
    "keyPoints": "理解课文的主要内容，体会作者的思想感情",
    "materials": "多媒体课件、草原风光图片、音频朗读材料",
    "methods": ["lecture", "discussion", "multimedia"],
    "steps": [
        {
            "title": "情境导入",
            "content": "播放草原风光视频，激发学习兴趣",
            "time": 8
        },
        {
            "title": "初读课文",
            "content": "学生自由朗读课文，整体感知",
            "time": 12
        },
        {
            "title": "深入分析",
            "content": "分段分析课文内容，理解重点词句",
            "time": 20
        },
        {
            "title": "总结升华",
            "content": "总结课文主题，升华情感",
            "time": 5
        }
    ],
    "homework": "1. 背诵课文第一段；2. 写一篇描写家乡风景的小作文",
    "reflection": "观察学生的学习反应和参与度"
}

print("测试数据准备完成")
print(f"测试数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")

def test_simple_version():
    """测试简化版服务"""
    print("\n" + "="*50)
    print("测试简化版服务（simple_app.py）")
    print("="*50)
    
    try:
        # 测试健康检查
        health_response = requests.get('http://localhost:5300/health', timeout=5)
        print(f"健康检查状态: {health_response.status_code}")
        
        if health_response.status_code != 200:
            print("❌ 简化版服务未运行，请先启动 simple_app.py")
            return False
        
        # 测试教案生成
        url = "http://localhost:5300/api/lesson-plan/generate"
        headers = {"Content-Type": "application/json"}
        
        print(f"发送请求到: {url}")
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 简化版API调用成功！")
            print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get("code") == 0:
                filename = result.get("data", {}).get("filename")
                if filename:
                    print(f"✅ 简化版教案生成成功！文件名: {filename}")
                    return True
                else:
                    print("❌ 响应中没有文件名")
                    return False
            else:
                print(f"❌ API返回错误: {result.get('message')}")
                return False
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 简化版测试失败: {e}")
        return False

def test_full_version():
    """测试完整版服务（包含DeepSeek API）"""
    print("\n" + "="*50)
    print("测试完整版服务（app.py with DeepSeek API）")
    print("="*50)
    
    try:
        # 首先检查是否有其他服务在5300端口运行
        try:
            health_response = requests.get('http://localhost:5300/health', timeout=3)
            if health_response.status_code == 200:
                print("⚠️  检测到5300端口有服务运行，可能是简化版服务")
                print("   请停止简化版服务后启动完整版服务进行测试")
                return False
        except:
            pass  # 端口没有服务运行，这是正常的
        
        # 测试完整版API（假设运行在5301端口避免冲突）
        url = "http://localhost:5301/api/lesson-plan/generate"
        headers = {"Content-Type": "application/json"}
        
        print(f"发送请求到: {url}")
        print("注意：请确保完整版服务运行在5301端口")
        
        response = requests.post(url, json=test_data, headers=headers, timeout=60)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 完整版API调用成功！")
            print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get("code") == 0:
                data = result.get("data", {})
                filename = data.get("filename")
                ai_success = data.get("ai_generation_success", False)
                ai_error = data.get("ai_error")
                
                if filename:
                    print(f"✅ 完整版教案生成成功！文件名: {filename}")
                    
                    if ai_success:
                        print("🤖 DeepSeek AI增强成功！")
                    else:
                        print(f"⚠️  DeepSeek AI增强失败: {ai_error}")
                        print("   但基础教案生成成功")
                    
                    return True
                else:
                    print("❌ 响应中没有文件名")
                    return False
            else:
                print(f"❌ API返回错误: {result.get('message')}")
                return False
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectRefused:
        print("❌ 无法连接到完整版服务")
        print("   请确保完整版服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 完整版测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试DeepSeek API集成...")
    
    # 测试简化版
    simple_success = test_simple_version()
    
    # 测试完整版
    full_success = test_full_version()
    
    print("\n" + "="*50)
    print("测试结果总结")
    print("="*50)
    
    if simple_success:
        print("✅ 简化版服务测试通过")
    else:
        print("❌ 简化版服务测试失败")
    
    if full_success:
        print("✅ 完整版服务（DeepSeek API）测试通过")
    else:
        print("❌ 完整版服务测试失败或未运行")
    
    print("\n📋 使用建议:")
    print("1. 如果只需要基础功能，使用简化版服务（simple_app.py）")
    print("2. 如果需要AI增强功能，使用完整版服务（app.py）")
    print("3. 完整版服务需要配置DeepSeek API密钥")
    
    if simple_success or full_success:
        print("\n🎉 至少有一个版本可以正常工作！")
    else:
        print("\n⚠️  所有版本都有问题，请检查服务状态")

if __name__ == "__main__":
    main()
