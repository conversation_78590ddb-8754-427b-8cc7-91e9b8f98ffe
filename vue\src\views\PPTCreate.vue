<template>
  <div class="ppt-create-container">
    <div class="layout-container">
      <!-- 左侧面板：标题和版本选择 -->
      <div class="sidebar">
        <div class="form-header">
          <div class="form-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z"></path>
              <line x1="12" y1="2" x2="12" y2="6"></line>
              <line x1="12" y1="18" x2="12" y2="22"></line>
              <line x1="4" y1="12" x2="20" y2="12"></line>
            </svg>
          </div>
          <div class="form-title">创建教学/展示PPT</div>
        </div>

        <!-- 版本选择开关 -->
        <div class="form-group version-selector">
          <label>PPT生成版本</label>
          <div class="version-toggle">
            <div
              class="version-option"
              :class="{ active: pptVersion === 'simple' }"
              @click="pptVersion = 'simple'"
            >
              <div class="version-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                  <polyline points="13 2 13 9 20 9"></polyline>
                </svg>
              </div>
              <div class="version-details">
                <div class="version-title">简易版</div>
                <div class="version-desc">自定义所有选项，直接下载完整PPT文件</div>
              </div>
            </div>
            <div
              class="version-option"
              :class="{ active: pptVersion === 'advanced' }"
              @click="pptVersion = 'advanced'"
            >
              <div class="version-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
              </div>
              <div class="version-details">
                <div class="version-title">高级版</div>
                <div class="version-desc">仅需填写基本信息，生成后可在线编辑</div>
              </div>
            </div>
          </div>
        </div>

        <div class="generation-info">
          <div class="info-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="16" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
          </div>
          <div class="info-text">
            生成过程可能需要较长时间，请耐心等待，不要关闭页面
          </div>
        </div>
      </div>

      <!-- 右侧面板：表单内容 -->
      <div class="main-content">
        <!-- 根据选择的版本显示不同的组件 -->
        <SimplePPTCreate
          v-if="pptVersion === 'simple'"
          :token="token"
        />

        <AdvancedPPTCreate
          v-if="pptVersion === 'advanced'"
          :token="token"
        />
      </div>
    </div>
  </div>
</template>

<script>
import SimplePPTCreate from '@/components/SimplePPTCreate.vue'
import AdvancedPPTCreate from '@/components/AdvancedPPTCreate.vue'

export default {
  name: 'PPTCreate',
  components: {
    SimplePPTCreate,
    AdvancedPPTCreate
  },
  data() {
    return {
      pptVersion: 'simple', // 默认使用简易版
      token: process.env.VUE_APP_DOCMEE_TOKEN || '', // 从环境变量获取 token
    }
  },
  created() {
    // 检查 token 是否存在
    if (!this.token) {
      console.error('Token not found:', this.token);
    } else {
      console.log('Token loaded:', this.token.substring(0, 10) + '...');
    }
  },
  methods: {
    // 处理高级版本生成PPT后的回调 - 不再需要，由AdvancedPPTCreate组件直接处理
    handlePptGenerated(pptId) {
      console.log('PPT已生成，ID:', pptId);
      // 不再需要显示内嵌编辑器
    }
  }
}
</script>

<style scoped>
@import '@/assets/css/form-styles.css';

.ppt-create-container {
  display: flex;
  height: 90vh;
  background-color: #f8f9fa;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.layout-container {
  display: flex;
  width: 95%;
  max-width: 1200px;
  height: 85vh;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 左侧边栏样式 */
.sidebar {
  width: 320px;
  background-color: #f8f9fa;
  padding: 25px;
  border-right: 1px solid #eaedf2;
  display: flex;
  flex-direction: column;
}

/* 右侧主内容区域 */
.main-content {
  flex: 1;
  padding: 25px 30px;
  overflow-y: auto;
}

.form-header {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eaedf2;
}

.form-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #42b983;
  border-radius: 10px;
  margin-right: 15px;
  color: white;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.generation-info {
  display: flex;
  align-items: flex-start;
  margin-top: auto;
  padding-top: 20px;
  margin-bottom: 15px;
  color: #5f6368;
  font-size: 14px;
  border-top: 1px solid #eaedf2;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  margin-top: 3px;
  color: #4285f4;
  flex-shrink: 0;
}

.info-text {
  line-height: 1.5;
}

/* 版本选择器样式 */
.version-selector {
  margin-top: 10px;
}

.version-toggle {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.version-option {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.version-option:hover {
  border-color: #42b983;
  background-color: rgba(66, 133, 244, 0.05);
}

.version-option.active {
  border-color: #42b983;
  background-color: rgba(66, 133, 244, 0.1);
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.15);
}

.version-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-right: 12px;
  color: #4285f4;
  flex-shrink: 0;
}

.version-option.active .version-icon {
  background-color: #42b983;
  color: white;
}

.version-details {
  flex: 1;
}

.version-title {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 4px;
  color: #333;
}

.version-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #eaedf2;
    padding: 20px;
  }

  .main-content {
    padding: 20px;
  }
}
</style>