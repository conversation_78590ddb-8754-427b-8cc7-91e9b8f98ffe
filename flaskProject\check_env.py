import sys
import os

print("Python 版本:", sys.version)
print("Python 路径:", sys.executable)
print("Python 模块搜索路径:")
for path in sys.path:
    print(f"  - {path}")

try:
    import flask_cors
    print("\nflask_cors 已安装:", flask_cors.__file__)
except ImportError:
    print("\nflask_cors 未安装或无法导入")

print("\n尝试安装 flask-cors:")
os.system(f"{sys.executable} -m pip install flask-cors")

print("\n安装后再次检查:")
try:
    import flask_cors
    print("flask_cors 已安装:", flask_cors.__file__)
except ImportError:
    print("flask_cors 仍未安装或无法导入")
