import request from '@/utils/request.js'
import axios from 'axios'

// 创建专门用于教案生成的axios实例
const lessonPlanRequest = axios.create({
  baseURL: 'http://localhost:5300/api',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 生成教案
export const generateLessonPlanService = (lessonPlanData) => {
  return lessonPlanRequest.post('/lesson-plan/generate', lessonPlanData)
    .then(response => response.data)
    .catch(error => {
      console.error('教案生成API错误:', error)
      throw error
    })
}

// 保存教案
export const saveLessonPlanService = (lessonPlanData) => {
  return request.post('/lesson-plan/save', lessonPlanData)
}

// 获取教案列表
export const getLessonPlanListService = (params) => {
  return request.get('/lesson-plan/list', { params })
}

// 获取教案详情
export const getLessonPlanDetailService = (id) => {
  return request.get(`/lesson-plan/${id}`)
}

// 更新教案
export const updateLessonPlanService = (id, lessonPlanData) => {
  return request.put(`/lesson-plan/${id}`, lessonPlanData)
}

// 删除教案
export const deleteLessonPlanService = (id) => {
  return request.delete(`/lesson-plan/${id}`)
}

// 导出教案
export const exportLessonPlanService = (id, format = 'pdf') => {
  return request.get(`/lesson-plan/${id}/export`, {
    params: { format },
    responseType: 'blob'
  })
}

// 获取教案模板
export const getLessonPlanTemplatesService = () => {
  return request.get('/lesson-plan/templates')
}

// AI智能优化教案
export const optimizeLessonPlanService = (lessonPlanData) => {
  return request.post('/lesson-plan/optimize', lessonPlanData)
}
