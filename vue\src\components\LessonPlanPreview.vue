<script>
import { computed } from 'vue'
import { Document, Download, Print } from '@element-plus/icons-vue'

export default {
  name: 'LessonPlanPreview',
  props: {
    lessonPlan: {
      type: Object,
      required: true
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'export', 'print'],
  setup(props, { emit }) {
    // 教学方法映射
    const methodLabels = {
      lecture: '讲授法',
      discussion: '讨论法',
      experiment: '实验法',
      demonstration: '演示法',
      practice: '练习法',
      case_study: '案例分析法',
      group_work: '小组合作',
      multimedia: '多媒体教学'
    }

    // 计算总时长
    const totalDuration = computed(() => {
      return props.lessonPlan.steps?.reduce((total, step) => total + step.time, 0) || 0
    })

    // 关闭对话框
    const handleClose = () => {
      emit('update:visible', false)
    }

    // 导出教案
    const handleExport = (format) => {
      emit('export', format)
    }

    // 打印教案
    const handlePrint = () => {
      emit('print')
    }

    // 获取教学方法标签
    const getMethodLabel = (method) => {
      return methodLabels[method] || method
    }

    return {
      totalDuration,
      handleClose,
      handleExport,
      handlePrint,
      getMethodLabel,
      Document,
      Download,
      Print
    }
  }
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="handleClose"
    title="教案预览"
    width="80%"
    class="lesson-preview-dialog"
  >
    <div class="lesson-preview">
      <!-- 教案标题 -->
      <div class="lesson-header">
        <h1>{{ lessonPlan.topic }}</h1>
        <div class="lesson-meta">
          <span>学科：{{ lessonPlan.subject }}</span>
          <span>年级：{{ lessonPlan.grade }}</span>
          <span>课时：{{ lessonPlan.duration }}分钟</span>
        </div>
      </div>

      <!-- 教学目标 -->
      <div class="lesson-section" v-if="lessonPlan.objectives?.length">
        <h3>教学目标</h3>
        <ul>
          <li v-for="(objective, index) in lessonPlan.objectives" :key="index">
            {{ objective }}
          </li>
        </ul>
      </div>

      <!-- 重点难点 -->
      <div class="lesson-section" v-if="lessonPlan.keyPoints">
        <h3>重点难点</h3>
        <p>{{ lessonPlan.keyPoints }}</p>
      </div>

      <!-- 教学方法 -->
      <div class="lesson-section" v-if="lessonPlan.methods?.length">
        <h3>教学方法</h3>
        <div class="methods-list">
          <el-tag v-for="method in lessonPlan.methods" :key="method" class="method-tag">
            {{ getMethodLabel(method) }}
          </el-tag>
        </div>
      </div>

      <!-- 教学材料 -->
      <div class="lesson-section" v-if="lessonPlan.materials">
        <h3>教学材料</h3>
        <p>{{ lessonPlan.materials }}</p>
      </div>

      <!-- 教学步骤 -->
      <div class="lesson-section" v-if="lessonPlan.steps?.length">
        <h3>教学步骤（总计：{{ totalDuration }}分钟）</h3>
        <div class="steps-timeline">
          <div v-for="(step, index) in lessonPlan.steps" :key="index" class="step-item">
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h4>{{ step.title }} <span class="step-time">（{{ step.time }}分钟）</span></h4>
              <p>{{ step.content }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 作业布置 -->
      <div class="lesson-section" v-if="lessonPlan.homework">
        <h3>作业布置</h3>
        <p>{{ lessonPlan.homework }}</p>
      </div>

      <!-- 教学反思 -->
      <div class="lesson-section" v-if="lessonPlan.reflection">
        <h3>教学反思</h3>
        <p>{{ lessonPlan.reflection }}</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handlePrint" :icon="Print">打印</el-button>
        <el-button @click="handleExport('pdf')" :icon="Download">导出PDF</el-button>
        <el-button @click="handleExport('word')" :icon="Document">导出Word</el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.lesson-preview-dialog {
  .lesson-preview {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;

    .lesson-header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #409eff;
      padding-bottom: 20px;

      h1 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 28px;
      }

      .lesson-meta {
        display: flex;
        justify-content: center;
        gap: 30px;
        color: #606266;
        font-size: 16px;
      }
    }

    .lesson-section {
      margin-bottom: 25px;

      h3 {
        color: #409eff;
        margin-bottom: 15px;
        font-size: 18px;
        border-left: 4px solid #409eff;
        padding-left: 10px;
      }

      p {
        line-height: 1.8;
        color: #606266;
        margin: 0;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          line-height: 1.8;
          color: #606266;
          margin-bottom: 8px;
        }
      }

      .methods-list {
        .method-tag {
          margin: 5px 5px 5px 0;
          background-color: #f0f9ff;
          border-color: #409eff;
        }
      }

      .steps-timeline {
        .step-item {
          display: flex;
          margin-bottom: 20px;

          .step-number {
            width: 30px;
            height: 30px;
            background-color: #409eff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
          }

          .step-content {
            flex: 1;

            h4 {
              margin: 0 0 10px 0;
              color: #303133;

              .step-time {
                color: #909399;
                font-weight: normal;
                font-size: 14px;
              }
            }

            p {
              margin: 0;
              line-height: 1.6;
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media print {
  .lesson-preview {
    .lesson-header {
      border-bottom: 2px solid #000;
    }

    .lesson-section h3 {
      border-left: 4px solid #000;
    }
  }
}
</style>
