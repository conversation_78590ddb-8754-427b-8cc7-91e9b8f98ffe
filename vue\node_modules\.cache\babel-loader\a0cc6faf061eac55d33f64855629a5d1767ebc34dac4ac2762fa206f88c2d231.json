{"ast": null, "code": "import axios from 'axios';\nimport { useTokenStore } from '@/stores/token.js';\n\n// 创建axios实例\nconst request = axios.create({\n  baseURL: '/api',\n  // 使用代理路径\n  timeout: 60000 // 请求超时时间增加到60秒\n});\n\n// 请求拦截器\nrequest.interceptors.request.use(config => {\n  // 在发送请求之前做些什么\n  const tokenStore = useTokenStore();\n  // 如果有token，添加到请求头\n  if (tokenStore.token) {\n    config.headers['Authorization'] = tokenStore.token;\n  }\n  return config;\n}, error => {\n  // 对请求错误做些什么\n  console.error('请求错误:', error);\n  return Promise.reject(error);\n});\n\n// 响应拦截器\nrequest.interceptors.response.use(response => {\n  // 对响应数据做点什么\n  return response.data;\n}, error => {\n  // 对响应错误做点什么\n  console.error('响应错误:', error);\n  return Promise.reject(error);\n});\nexport default request;", "map": {"version": 3, "names": ["axios", "useTokenStore", "request", "create", "baseURL", "timeout", "interceptors", "use", "config", "tokenStore", "token", "headers", "error", "console", "Promise", "reject", "response", "data"], "sources": ["D:/shishuo/vue/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\r\nimport { useTokenStore } from '@/stores/token.js'\r\n\r\n// 创建axios实例\r\nconst request = axios.create({\r\n  baseURL: '/api', // 使用代理路径\r\n  timeout: 60000 // 请求超时时间增加到60秒\r\n})\r\n\r\n// 请求拦截器\r\nrequest.interceptors.request.use(\r\n  config => {\r\n    // 在发送请求之前做些什么\r\n    const tokenStore = useTokenStore()\r\n    // 如果有token，添加到请求头\r\n    if (tokenStore.token) {\r\n      config.headers['Authorization'] = tokenStore.token\r\n    }\r\n    return config\r\n  },\r\n  error => {\r\n    // 对请求错误做些什么\r\n    console.error('请求错误:', error)\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 响应拦截器\r\nrequest.interceptors.response.use(\r\n  response => {\r\n    // 对响应数据做点什么\r\n    return response.data\r\n  },\r\n  error => {\r\n    // 对响应错误做点什么\r\n    console.error('响应错误:', error)\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nexport default request"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,mBAAmB;;AAEjD;AACA,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC3BC,OAAO,EAAE,MAAM;EAAE;EACjBC,OAAO,EAAE,KAAK,CAAC;AACjB,CAAC,CAAC;;AAEF;AACAH,OAAO,CAACI,YAAY,CAACJ,OAAO,CAACK,GAAG,CAC9BC,MAAM,IAAI;EACR;EACA,MAAMC,UAAU,GAAGR,aAAa,CAAC,CAAC;EAClC;EACA,IAAIQ,UAAU,CAACC,KAAK,EAAE;IACpBF,MAAM,CAACG,OAAO,CAAC,eAAe,CAAC,GAAGF,UAAU,CAACC,KAAK;EACpD;EACA,OAAOF,MAAM;AACf,CAAC,EACDI,KAAK,IAAI;EACP;EACAC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EAC7B,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAV,OAAO,CAACI,YAAY,CAACU,QAAQ,CAACT,GAAG,CAC/BS,QAAQ,IAAI;EACV;EACA,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACDL,KAAK,IAAI;EACP;EACAC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EAC7B,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAeV,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}