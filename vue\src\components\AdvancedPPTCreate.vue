<template>
  <div class="advanced-ppt-create">
    <div class="form-group">
      <label for="title">PPT标题 <span class="required">*</span></label>
      <input
        id="title"
        type="text"
        v-model="pptOptions.title"
        placeholder="请输入PPT标题"
      />
    </div>

    <div class="form-group">
      <label for="content">PPT内容描述 <span class="required">*</span></label>
      <textarea
        id="content"
        v-model="inputContent"
        placeholder="请详细描述您需要的PPT内容，包括主题、要点和特殊要求..."
        rows="5"
      ></textarea>
      <div class="input-tip">详细的描述将帮助AI生成更符合您需求的PPT，内容越详细，生成的PPT质量越高</div>
    </div>

    <div class="form-group">
      <label for="slide_count">幻灯片数量</label>
      <input
        id="slide_count"
        type="number"
        v-model="pptOptions.slide_count"
        min="10"
        max="30"
        placeholder="建议15-20张"
      />
    </div>

    <div class="form-actions">
      <button
        @click="generatePPT"
        :disabled="isLoading"
        class="generate-btn"
      >
        <span v-if="isLoading" class="loading-spinner"></span>
        {{ isLoading ? '正在生成PPT...' : '生成PPT' }}
      </button>

      <!-- 测试按钮 -->
      <button
        @click="testEditor"
        class="test-btn"
      >
        测试编辑器
      </button>
    </div>

    <div v-if="errorMessage" class="error-message">
      <div class="error-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
      </div>
      <div class="error-text">{{ errorMessage }}</div>
    </div>

    <!-- 成功消息 -->
    <div v-if="successMessage" class="success-message">
      <div class="success-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
      </div>
      <div class="success-text">
        <div>{{ successMessage }}</div>
        <button v-if="generatedPptId" class="open-editor-btn" @click="openEditor(generatedPptId)">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="9" y1="3" x2="9" y2="21"></line>
          </svg>
          <span>查看</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdvancedPPTCreate',
  props: {
    token: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      inputContent: '',
      isLoading: false,
      errorMessage: '',
      successMessage: '',
      generatedPptId: '',
      pptOptions: {
        title: '',
        slide_count: 15,
        template_style: 'professional',
        color_theme: 'blue',
        font_family: '微软雅黑',
      }
    }
  },
  methods: {
    async generatePPT() {
      if (!this.token) {
        this.errorMessage = '未配置 Docmee token，请在环境变量中设置 VUE_APP_DOCMEE_TOKEN';
        return;
      }
      if (this.inputContent.trim() === '') {
        this.errorMessage = '请输入PPT内容描述';
        return;
      }

      this.isLoading = true;
      this.errorMessage = '';
      this.successMessage = '';
      this.generatedPptId = '';

      try {
        // 准备表单数据
        const formData = new FormData();
        formData.append('content', this.inputContent.trim());
        formData.append('title', this.pptOptions.title || '');
        formData.append('slide_count', this.pptOptions.slide_count || '15');
        formData.append('version', 'advanced');

        const response = await fetch('http://localhost:5300/api/ppt', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          const data = await response.json();

          if (data.pptId) {
            // 生成PPT成功
            console.log('生成PPT成功，pptId:', data.pptId);
            // 保存生成的pptId
            this.generatedPptId = data.pptId;
            this.successMessage = 'PPT生成成功！点击查看。';
            // 确保isLoading设置为false
            this.isLoading = false;
            // 创建一个预加载的隐藏iframe，以便后续点击时能快速打开
            this.preloadEditorPage(data.pptId);
          } else {
            this.errorMessage = '生成失败：未获取到 PPT ID';
          }
        } else {
          const error = await response.json();
          this.errorMessage = '生成失败: ' + (error.error || '未知错误');
          console.error('生成PPT失败:', error);
        }
      } catch (error) {
        console.error('生成PPT出错:', error);
        if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
          this.errorMessage = '无法连接到服务器，请检查网络连接或确保后端服务正在运行';
        } else if (error.name === 'AbortError') {
          this.errorMessage = '请求超时，请稍后重试';
        } else {
          this.errorMessage = '生成PPT时出错，请稍后重试';
        }
      } finally {
        this.isLoading = false;
      }
    },

    // 打开编辑器页面
    openEditor(pptId) {
      if (!this.token) {
        this.errorMessage = '未配置 Docmee token，请在环境变量中设置 VUE_APP_DOCMEE_TOKEN';
        return;
      }
      if (!pptId) {
        this.errorMessage = '缺少PPT ID，无法打开编辑器';
        return;
      }

      console.log('打开编辑器，pptId:', pptId, 'token:', this.token.substring(0, 10) + '...');
      // 构建编辑器URL，包含token和pptId参数
      const editorUrl = `/ppt-editor.html?token=${encodeURIComponent(this.token)}&pptId=${encodeURIComponent(pptId)}&animation=true`;
      try {
        const newWindow = window.open(editorUrl, '_blank');
        // 检查窗口是否成功打开
        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
          this.errorMessage = '无法打开编辑器，请检查浏览器是否阻止了弹出窗口';
          console.error('无法打开编辑器窗口，可能被浏览器阻止');
        } else {
          this.errorMessage = '';
          this.generatedPptId = pptId;
          this.successMessage = 'PPT生成成功！编辑器已在新窗口中打开。';
          // 重置加载状态
          this.isLoading = false;

          // 添加成功提示
          this.$nextTick(() => {
            console.log('编辑器已在新窗口中打开');
          });
        }
      } catch (error) {
        console.error('打开编辑器窗口时出错:', error);
        this.errorMessage = '打开编辑器时出错: ' + error.message;
        this.isLoading = false;
      }
    },
    // 预加载编辑器页面
    preloadEditorPage(pptId) {
      if (!this.token || !pptId) return;
      const editorUrl = `/ppt-editor.html?token=${encodeURIComponent(this.token)}&pptId=${encodeURIComponent(pptId)}&animation=true`;
      // 创建一个隐藏的iframe来预加载页面
      const preloadFrame = document.createElement('iframe');
      preloadFrame.style.display = 'none';
      preloadFrame.src = editorUrl;
      document.body.appendChild(preloadFrame);
      setTimeout(() => {
        if (document.body.contains(preloadFrame)) {
          document.body.removeChild(preloadFrame);
        }
      }, 5000);

      console.log('编辑器页面预加载中...');
    },

    // 测试编辑器功能
    testEditor() {
      if (!this.token) {
        this.errorMessage = '未配置 Docmee token，请在环境变量中设置 VUE_APP_DOCMEE_TOKEN';
        return;
      }

      // 重置消息
      this.errorMessage = '';
      this.successMessage = '';

      // 使用一个固定的测试ID
      const testPptId = '1921249442112081920'; // 可以替换为任何有效的测试ID

      // 直接打开编辑器
      this.openEditor(testPptId);

      // 设置成功消息
      this.successMessage = '测试编辑器已在新窗口中打开。';
    }
  }
}
</script>

<style scoped>
@import '@/assets/css/form-styles.css';

.advanced-ppt-create {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 调整表单元素的样式 */
.form-group {
  margin-bottom: 20px;
}

/* 调整文本区域的高度 */
textarea {
  min-height: 180px;
}

/* 调整按钮容器 */
.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.test-btn {
  padding: 12px 24px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 180px;
  box-shadow: 0 2px 5px rgba(76, 175, 80, 0.2);
}

.test-btn:hover {
  background-color: #45a049;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.success-message {
  display: flex;
  align-items: center;
  margin-top: 20px;
  padding: 12px 15px;
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 8px;
  font-size: 14px;
  border-left: 3px solid #4caf50;
}

.success-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #4caf50;
}

.success-text {
  flex: 1;
}

.open-editor-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  padding: 10px 16px;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(66, 133, 244, 0.3);
}

.open-editor-btn:hover {
  background-color: #42b983;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 133, 244, 0.4);
}

.open-editor-btn svg {
  margin-right: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
    align-items: center;
  }

  .test-btn, .generate-btn {
    width: 100%;
    margin-top: 10px;
  }
}
</style>
