<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <title>文多多 AiPPT 编辑器</title>
    <script src="static/docmee-ui-sdk-iframe.min.js"></script>
    <style>
      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }
      #container {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }
      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        font-family: Arial, sans-serif;
      }
      .loading-spinner {
        display: inline-block;
        width: 50px;
        height: 50px;
        border: 5px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #4285f4;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 20px;
      }
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- 加载提示 -->
    <div id="loading" class="loading">
      <div class="loading-spinner"></div>
      <div>正在加载编辑器...</div>
    </div>

    <!-- 挂载iframe容器 -->
    <div id="container"></div>
  </body>
  <script>
    // 从URL获取参数
    function getQueryParams() {
      const params = {};
      const queryString = window.location.search.substring(1);
      const pairs = queryString.split('&');

      for (let i = 0; i < pairs.length; i++) {
        const pair = pairs[i].split('=');
        params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
      }

      return params;
    }

    // 检查协议
    if (location.protocol == 'file:') {
      alert('不支持 file 协议直接访问，请启动 http 服务访问！');
      document.getElementById('loading').innerHTML = '<div style="color: red;">不支持 file 协议直接访问</div>';
    }

    // 获取URL参数
    const params = getQueryParams();
    const token = params.token || ''; // 从URL参数获取token
    const pptId = params.pptId || ''; // 从URL参数获取pptId
    const animation = params.animation !== 'false'; // 从URL参数获取animation设置

    // 检查参数
    if (!token || !pptId) {
      document.getElementById('loading').innerHTML = '<div style="color: red;">缺少必要参数：token 和 pptId</div>';
      console.error('缺少必要参数：token 和 pptId');
    } else {
      console.log('初始化编辑器，pptId:', pptId);

      try {
        // 初始化 UI iframe
        const docmeeUI = new DocmeeUI({
          pptId: pptId,
          token: token,
          animation: animation,
          container: document.querySelector('#container'),
          page: 'editor',
          lang: 'zh',
          mode: 'light',
          isMobile: false,
          background: 'linear-gradient(-157deg,#f57bb0, #867dea)',
          padding: '40px 20px 0px',
          onMessage(message) {
            console.log('编辑器消息:', message);

            // 隐藏加载提示
            if (message.type === 'mounted') {
              document.getElementById('loading').style.display = 'none';
            }

            if (message.type === 'beforeDownload') {
              // 自定义下载PPT的文件名称
              const { id, subject } = message.data;
              return `PPT_${subject || 'presentation'}.pptx`;
            } else if (message.type === 'invalid-token') {
              // 在token失效时触发
              console.error('token 认证错误');
              document.getElementById('loading').innerHTML = '<div style="color: red;">Token认证失败，请检查Token是否有效</div>';
              document.getElementById('loading').style.display = 'block';
            }
          }
        });

        // 将docmeeUI实例保存到window对象，以便外部访问
        window.docmeeUI = docmeeUI;

      } catch (error) {
        console.error('初始化编辑器失败:', error);
        document.getElementById('loading').innerHTML = `<div style="color: red;">初始化编辑器失败: ${error.message}</div>`;
      }
    }
  </script>
</html>
