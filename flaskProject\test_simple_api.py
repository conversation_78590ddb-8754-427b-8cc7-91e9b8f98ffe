#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

# 测试数据
test_data = {
    "subject": "数学",
    "grade": "五年级",
    "topic": "分数的加减法",
    "duration": 45,
    "objectives": [
        "掌握分数加减法的计算方法",
        "能够解决简单的分数应用题",
        "培养数学思维能力"
    ],
    "keyPoints": "分数加减法的计算规则，通分的方法",
    "materials": "分数卡片、计算器、练习题",
    "steps": [
        {
            "title": "复习导入",
            "content": "复习分数的基本概念",
            "time": 10
        },
        {
            "title": "新课讲解",
            "content": "讲解分数加减法的计算方法",
            "time": 25
        },
        {
            "title": "练习巩固",
            "content": "学生练习分数加减法计算",
            "time": 10
        }
    ],
    "homework": "完成练习册第15-20题",
    "reflection": "观察学生掌握情况"
}

print("测试简化版教案生成API...")
print(f"测试数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")

try:
    # 发送POST请求到教案生成API
    url = "http://localhost:5300/api/lesson-plan/generate"
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"发送请求到: {url}")
    response = requests.post(url, json=test_data, headers=headers, timeout=10)
    
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ API调用成功！")
        print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get("code") == 0:
            filename = result.get("data", {}).get("filename")
            if filename:
                print(f"✅ 教案生成成功！文件名: {filename}")
                
                # 测试文件下载
                download_url = f"http://localhost:5300/api/lesson-plan/download/{filename}"
                print(f"测试文件下载: {download_url}")
                
                download_response = requests.get(download_url, timeout=5)
                print(f"下载响应状态码: {download_response.status_code}")
                
                if download_response.status_code == 200:
                    print("✅ 文件下载成功！")
                    print(f"文件大小: {len(download_response.content)} 字节")
                    
                    # 保存文件到本地进行验证
                    with open(f"test_{filename}", "wb") as f:
                        f.write(download_response.content)
                    print(f"✅ 文件已保存为: test_{filename}")
                else:
                    print(f"❌ 文件下载失败: {download_response.text}")
            else:
                print("❌ 响应中没有文件名")
        else:
            print(f"❌ API返回错误: {result.get('message')}")
    else:
        print(f"❌ API调用失败，状态码: {response.status_code}")
        print(f"错误信息: {response.text}")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
