#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import json
import requests
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🎓 测试PPT风格的教案生成功能...")

# 测试数据（模仿PPT测试数据的格式）
test_data = {
    "subject": "数学",
    "grade": "六年级",
    "topic": "圆的面积计算",
    "duration": 45,
    "objectives": [
        "理解圆的面积计算公式的推导过程",
        "掌握圆的面积计算方法",
        "能够解决与圆的面积相关的实际问题",
        "培养学生的空间想象能力和逻辑思维能力"
    ],
    "keyPoints": "圆的面积公式推导，圆的面积计算方法",
    "materials": "圆形教具、多媒体课件、计算器、练习题",
    "methods": ["lecture", "demonstration", "practice", "group_work"],
    "steps": [
        {
            "title": "复习导入",
            "content": "复习圆的周长计算，引入面积概念",
            "time": 8
        },
        {
            "title": "公式推导",
            "content": "通过分割圆形，推导面积计算公式",
            "time": 20
        },
        {
            "title": "例题讲解",
            "content": "讲解典型例题，掌握计算方法",
            "time": 12
        },
        {
            "title": "练习巩固",
            "content": "学生练习，教师指导",
            "time": 5
        }
    ],
    "homework": "完成练习册第45-48页的习题",
    "reflection": "观察学生对公式推导过程的理解程度"
}

def test_simple_lesson_plan():
    """测试简单版教案生成（模仿SimplePPTResource）"""
    print("\n" + "="*60)
    print("🚀 测试简单版教案生成服务")
    print("="*60)
    
    try:
        # 测试简单版教案生成
        url = "http://localhost:5300/api/lesson-plan/simple"
        headers = {"Content-Type": "application/json"}
        
        print(f"1. 发送请求到: {url}")
        print(f"   课题: {test_data['subject']} - {test_data['topic']}")
        
        start_time = time.time()
        response = requests.post(url, json=test_data, headers=headers, timeout=60)
        end_time = time.time()
        
        print(f"   响应状态码: {response.status_code}")
        print(f"   响应时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            # 简单版应该直接返回文件
            print("   ✅ 简单版教案生成成功！")
            print(f"   文件大小: {len(response.content)} 字节")
            
            # 保存文件
            filename = f"simple_lesson_{test_data['topic']}.docx"
            with open(filename, "wb") as f:
                f.write(response.content)
            print(f"   📄 文件已保存为: {filename}")
            
            return True
        else:
            print(f"   ❌ 简单版教案生成失败")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_advanced_lesson_plan():
    """测试高级版教案生成（模仿AdvancedPPTResource）"""
    print("\n" + "="*60)
    print("🚀 测试高级版教案生成服务")
    print("="*60)
    
    try:
        # 测试高级版教案生成
        url = "http://localhost:5300/api/lesson-plan/generate"
        headers = {"Content-Type": "application/json"}
        
        print(f"1. 发送请求到: {url}")
        print(f"   课题: {test_data['subject']} - {test_data['topic']}")
        
        start_time = time.time()
        response = requests.post(url, json=test_data, headers=headers, timeout=120)
        end_time = time.time()
        
        print(f"   响应状态码: {response.status_code}")
        print(f"   响应时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 高级版教案生成成功！")
            print(f"   响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get("status") == "success":
                filename = result.get("filename")
                ai_success = result.get("ai_generation_success", False)
                
                print(f"   📄 生成文件: {filename}")
                print(f"   🤖 DeepSeek AI增强: {'✅ 成功' if ai_success else '❌ 失败'}")
                
                if filename:
                    # 测试文件下载
                    download_url = f"http://localhost:5300/api/lesson-plan/download/{filename}"
                    print(f"\n2. 测试文件下载: {download_url}")
                    
                    download_response = requests.get(download_url, timeout=10)
                    if download_response.status_code == 200:
                        print(f"   ✅ 文件下载成功")
                        print(f"   文件大小: {len(download_response.content)} 字节")
                        
                        # 保存文件
                        local_filename = f"advanced_{filename}"
                        with open(local_filename, "wb") as f:
                            f.write(download_response.content)
                        print(f"   📄 文件已保存为: {local_filename}")
                        
                        return True
                    else:
                        print(f"   ❌ 文件下载失败: {download_response.text}")
                        return False
                else:
                    print("   ❌ 响应中没有文件名")
                    return False
            else:
                print(f"   ❌ 生成失败: {result}")
                return False
        else:
            print(f"   ❌ 高级版教案生成失败")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_health_check():
    """测试健康检查"""
    try:
        response = requests.get('http://localhost:5300/health', timeout=5)
        if response.status_code == 200:
            print("✅ 服务正常运行")
            return True
        else:
            print("❌ 服务状态异常")
            return False
    except:
        print("❌ 无法连接到服务")
        return False

def main():
    """主函数"""
    print("🎯 PPT风格的教案生成系统测试")
    print("完全模仿PPT生成功能的实现方式")
    
    # 显示测试数据
    print(f"\n📋 测试数据预览:")
    print(f"学科: {test_data['subject']}")
    print(f"年级: {test_data['grade']}")
    print(f"课题: {test_data['topic']}")
    print(f"教学目标: {len(test_data['objectives'])}个")
    print(f"教学步骤: {len(test_data['steps'])}个")
    
    # 检查服务状态
    print(f"\n🔍 检查服务状态...")
    if not test_health_check():
        print("\n❌ 服务未运行，请先启动 app.py")
        return
    
    # 执行测试
    simple_success = test_simple_lesson_plan()
    advanced_success = test_advanced_lesson_plan()
    
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    if simple_success:
        print("✅ 简单版教案生成测试成功")
    else:
        print("❌ 简单版教案生成测试失败")
    
    if advanced_success:
        print("✅ 高级版教案生成测试成功")
    else:
        print("❌ 高级版教案生成测试失败")
    
    if simple_success or advanced_success:
        print("\n🎉 至少有一个版本可以正常工作！")
        print("\n✨ 功能特点:")
        print("   - 完全模仿PPT生成功能的实现方式")
        print("   - 使用相同的DeepSeek API调用方法")
        print("   - 支持简单版和高级版两种模式")
        print("   - 生成专业的Word格式教案")
        
        print("\n🔧 API端点:")
        print("   - 简单版: /api/lesson-plan/simple")
        print("   - 高级版: /api/lesson-plan/generate")
        print("   - 下载: /api/lesson-plan/download/<filename>")
        
    else:
        print("\n⚠️  所有版本都有问题，请检查:")
        print("   1. 服务是否正在运行 (python app.py)")
        print("   2. DeepSeek API密钥是否配置正确")
        print("   3. 网络连接是否正常")

if __name__ == "__main__":
    main()
