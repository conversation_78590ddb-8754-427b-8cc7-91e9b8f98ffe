import os
import logging
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载.env文件中的环境变量
load_dotenv()

class Config:
    """应用配置类"""

    # 应用基本配置
    PORT = int(os.getenv("PORT", 5300))
    DEBUG = os.getenv("DEBUG", "True").lower() in ("true", "1", "t")

    # 文件上传配置
    UPLOAD_FOLDER = os.path.abspath(os.getenv("UPLOAD_FOLDER", "temp_ppt"))

    # DeepSeek API配置
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
    DEEPSEEK_BASE_URL = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com")

    # Moonshot API配置
    MOONSHOT_API_KEY = os.getenv("MOONSHOT_API_KEY", "")
    MOONSHOT_BASE_URL = os.getenv("MOONSHOT_BASE_URL", "https://api.moonshot.cn/v1")

    # Docmee API配置
    DOCMEE_API_KEY = os.getenv("DOCMEE_API_KEY", "")
    DOCMEE_USER_ID = os.getenv("DOCMEE_USER_ID", "test")
    DOCMEE_API_TOKEN = os.getenv("DOCMEE_API_TOKEN", "")

    @classmethod
    def init_app(cls):
        """初始化应用配置"""
        # 创建上传目录
        if not os.path.exists(cls.UPLOAD_FOLDER):
            os.makedirs(cls.UPLOAD_FOLDER)
            logger.info(f"创建上传目录: {cls.UPLOAD_FOLDER}")

        # 验证必要的配置
        if not cls.DEEPSEEK_API_KEY:
            logger.warning("DeepSeek API密钥未设置")

        if not cls.MOONSHOT_API_KEY:
            logger.warning("Moonshot API密钥未设置")

        return cls

# 初始化配置
config = Config.init_app()
