import os
import uuid
import logging
from flask import Flask, Response, request, jsonify, send_file
from flask_cors import CORS
from flask_restful import Api, Resource
from openai import OpenAI
import json
from services.ppt_service import PPTService
from services.lesson_plan_service import LessonPlanService
from config import config

# 使用配置模块中的日志配置
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})  # 允许所有来源的跨域请求
api = Api(app)

# 使用配置模块中的上传目录
UPLOAD_FOLDER = config.UPLOAD_FOLDER

# 配置API客户端
client = OpenAI(
    api_key=config.DEEPSEEK_API_KEY,
    base_url=config.DEEPSEEK_BASE_URL
)
kimi = OpenAI(
    api_key=config.MOONSHOT_API_KEY,
    base_url=config.MOONSHOT_BASE_URL
)

# 初始化PPT服务
ppt_service = PPTService(
    api_key=config.DEEPSEEK_API_KEY,
    base_url=config.DEEPSEEK_BASE_URL,
    upload_folder=UPLOAD_FOLDER
)

# 初始化教案服务
lesson_plan_service = LessonPlanService(
    api_key=config.DEEPSEEK_API_KEY,
    base_url=config.DEEPSEEK_BASE_URL,
    upload_folder=UPLOAD_FOLDER
)

class SimplePPTResource(Resource):
    def post(self):
        try:
            # 获取表单数据
            content = request.form.get('content')
            title = request.form.get('title', '')
            slide_count = int(request.form.get('slide_count', 15))
            template_style = request.form.get('template_style', 'professional')
            color_theme = request.form.get('color_theme', 'blue')
            font_family = request.form.get('font_family', '微软雅黑')
            version = request.form.get('version', 'simple')  # 获取版本参数

            if not content:
                return {"error": "Missing content parameter"}, 400

            logger.info(f"开始生成PPT，标题：{title}，页数：{slide_count}，版本：{version}")

            # 使用PPT服务生成PPT
            result = ppt_service.generate_Simple_ppt(
                content=content,
                title=title,
                slide_count=slide_count,
                template_style=template_style,
                color_theme=color_theme,
                font_family=font_family
            )
            if "error" in result:
                return {"error": result["error"]}, 500

            # 直接返回生成的PPT文件
            return send_file(
                result["file_path"],
                as_attachment=True,
                download_name=result["filename"]
            )

        except Exception as e:
            logger.error(f"生成PPT时出错: {str(e)}")
            return {
                "error": f"生成PPT时出错: {str(e)}"
            }, 500

class ChatResource(Resource):
    def get(self):
        """处理聊天请求"""
        content = request.args.get('content')
        if not content:
            return {"error": "Missing content parameter"}, 400

        print(f"收到用户消息: {content}")

        messages = [

            {"role": "system", "content": "你是一个师说教学辅助系统中的知识问答助手"},
            {"role": "user", "content": content},
        ]

        def generate_stream():

                print("开始调用 DeepSeek API...")
                response = client.chat.completions.create(
                    model="deepseek-reasoner",
                    messages=messages,
                    stream=True,
                    temperature=0.7,
                    top_p=0.9,

                )
                for chunk in response:
                    content = chunk.choices[0].delta.content
                    if content:
                        yield f"data: {json.dumps({'content': content})}\n\n"

                yield "data: [END]\n\n"

        return Response(
            generate_stream(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no'
            }
        )



# 创建高级版PPT资源类
class AdvancedPPTResource(Resource):
    def post(self):
        try:
            # 获取表单数据
            content = request.form.get('content')
            title = request.form.get('title', '')
            slide_count = int(request.form.get('slide_count', 15))

            if not content:
                return {"error": "Missing content parameter"}, 400

            logger.info(f"开始生成高级版PPT，标题：{title}，页数：{slide_count}")

            # 使用PPT服务生成PPT
            result = ppt_service.generate_Advanced_ppt(
                content=content,
                title=title,
                slide_count=slide_count,
            )

            # 检查是否有错误
            if "error" in result:
                logger.error(f"生成高级版PPT失败: {result['error']}")
                return {"error": result["error"]}, 500

            logger.info(f"生成高级版PPT成功: {result}")

            # 返回pptId和状态
            response_data = {
                "status": "success",
                "pptId": result.get("pptId", ""),
                "taskId": result.get("taskId", "")
            }

            # 如果有pptInfo，添加到响应中
            if "pptInfo" in result and result["pptInfo"]:
                ppt_info = result["pptInfo"]
                # 添加一些有用的信息
                if isinstance(ppt_info, dict):
                    response_data["subject"] = ppt_info.get("subject", "")
                    response_data["coverUrl"] = ppt_info.get("coverUrl", "")
                    # 可以根据需要添加更多字段

            logger.info(f"返回前端数据: {response_data}")
            return response_data

        except Exception as e:
            logger.error(f"生成高级版PPT时出错: {str(e)}")
            return {
                "error": f"生成PPT时出错: {str(e)}"
            }, 500



# 简单版教案生成资源类（模仿SimplePPTResource）
class SimpleLessonPlanResource(Resource):
    def post(self):
        try:
            # 获取JSON数据（与PPT不同，教案使用JSON而不是表单数据）
            data = request.get_json()
            if not data:
                return {"error": "Missing JSON data"}, 400

            # 获取必要参数
            subject = data.get('subject', '')
            grade = data.get('grade', '')
            topic = data.get('topic', '')
            objectives = data.get('objectives', [])

            if not subject or not grade or not topic:
                return {"error": "Missing required parameters: subject, grade, topic"}, 400

            if not objectives or len(objectives) == 0:
                return {"error": "Missing teaching objectives"}, 400

            logger.info(f"开始生成简单版教案，学科：{subject}，年级：{grade}，课题：{topic}")

            # 使用教案服务生成Word文档（模仿PPT服务调用方式）
            result = lesson_plan_service.generate_lesson_plan_word(data)

            if "error" in result:
                return {"error": result["error"]}, 500

            # 直接返回生成的Word文件（模仿PPT的返回方式）
            return send_file(
                result["file_path"],
                as_attachment=True,
                download_name=result["filename"]
            )

        except Exception as e:
            logger.error(f"生成简单版教案时出错: {str(e)}")
            return {
                "error": f"生成教案时出错: {str(e)}"
            }, 500


# 高级版教案生成资源类（模仿AdvancedPPTResource）
class AdvancedLessonPlanResource(Resource):
    def post(self):
        try:
            # 获取JSON数据
            data = request.get_json()
            if not data:
                return {"error": "Missing JSON data"}, 400

            # 获取必要参数
            subject = data.get('subject', '')
            grade = data.get('grade', '')
            topic = data.get('topic', '')
            objectives = data.get('objectives', [])

            if not subject or not grade or not topic:
                return {"error": "Missing required parameters: subject, grade, topic"}, 400

            if not objectives or len(objectives) == 0:
                return {"error": "Missing teaching objectives"}, 400

            logger.info(f"开始生成高级版教案，学科：{subject}，年级：{grade}，课题：{topic}")

            # 使用教案服务生成Word文档（模仿高级版PPT的调用方式）
            result = lesson_plan_service.generate_lesson_plan_word(data)

            # 检查是否有错误
            if "error" in result:
                logger.error(f"生成高级版教案失败: {result['error']}")
                return {"error": result["error"]}, 500

            logger.info(f"生成高级版教案成功: {result}")

            # 返回文件信息和状态（模仿高级版PPT的返回格式）
            response_data = {
                "status": "success",
                "filename": result.get("filename", ""),
                "file_path": result.get("file_path", ""),
                "ai_generation_success": result.get("enhanced_content", {}).get("ai_generation_success", False)
            }

            # 如果有增强内容，添加到响应中
            if "enhanced_content" in result and result["enhanced_content"]:
                enhanced_content = result["enhanced_content"]
                if isinstance(enhanced_content, dict):
                    response_data["subject"] = enhanced_content.get("subject", subject)
                    response_data["grade"] = enhanced_content.get("grade", grade)
                    response_data["topic"] = enhanced_content.get("topic", topic)
                    response_data["ai_enhanced"] = enhanced_content.get("ai_generation_success", False)

            logger.info(f"返回前端数据: {response_data}")
            return response_data

        except Exception as e:
            logger.error(f"生成高级版教案时出错: {str(e)}")
            return {
                "error": f"生成教案时出错: {str(e)}"
            }, 500


# 教案文件下载资源类
class LessonPlanDownloadResource(Resource):
    def get(self, filename):
        try:
            # 构建文件路径
            file_path = os.path.join(UPLOAD_FOLDER, filename)

            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {"error": "文件不存在"}, 404

            # 返回文件
            return send_file(
                file_path,
                as_attachment=True,
                download_name=filename
            )

        except Exception as e:
            logger.error(f"下载教案文件失败: {str(e)}")
            return {"error": f"下载失败: {str(e)}"}, 500


# 注册路由
api.add_resource(ChatResource, '/api/chat')
api.add_resource(SimplePPTResource, '/api/ppt/simple')
api.add_resource(AdvancedPPTResource, '/api/ppt')
api.add_resource(SimpleLessonPlanResource, '/api/lesson-plan/simple')
api.add_resource(AdvancedLessonPlanResource, '/api/lesson-plan/generate')
api.add_resource(LessonPlanDownloadResource, '/api/lesson-plan/download/<string:filename>')

if __name__ == '__main__':
    # 在解决watchdog版本问题后，可以恢复调试模式
    app.run(host='0.0.0.0', port=config.PORT, debug=config.DEBUG)  # 使用配置模块中的端口和调试模式