#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """启动教案生成服务"""
    try:
        print("=" * 50)
        print("🎓 启动PPT风格的教案生成服务")
        print("=" * 50)
        
        # 检查环境
        print("1. 检查环境...")
        
        # 检查必要的模块
        try:
            from flask import Flask
            print("   ✅ Flask 已安装")
        except ImportError:
            print("   ❌ Flask 未安装，请运行: pip install flask")
            return
        
        try:
            from openai import OpenAI
            print("   ✅ OpenAI 已安装")
        except ImportError:
            print("   ❌ OpenAI 未安装，请运行: pip install openai")
            return
        
        try:
            from docx import Document
            print("   ✅ python-docx 已安装")
        except ImportError:
            print("   ❌ python-docx 未安装，请运行: pip install python-docx")
            return
        
        # 检查配置
        print("\n2. 检查配置...")
        try:
            from config import config
            print(f"   端口: {config.PORT}")
            print(f"   上传目录: {config.UPLOAD_FOLDER}")
            print(f"   DeepSeek API: {'已配置' if config.DEEPSEEK_API_KEY else '未配置'}")
            
            if not config.DEEPSEEK_API_KEY:
                print("   ⚠️  DeepSeek API密钥未配置，将使用基础模式")
        except Exception as e:
            print(f"   ❌ 配置加载失败: {e}")
            return
        
        # 启动服务
        print("\n3. 启动服务...")
        try:
            from app import app
            print("   ✅ 应用模块加载成功")
            
            print(f"\n🚀 服务启动中...")
            print(f"   地址: http://localhost:{config.PORT}")
            print(f"   健康检查: http://localhost:{config.PORT}/health")
            print(f"   简单版教案: POST /api/lesson-plan/simple")
            print(f"   高级版教案: POST /api/lesson-plan/generate")
            print("=" * 50)
            
            # 启动Flask应用
            app.run(host='0.0.0.0', port=config.PORT, debug=config.DEBUG)
            
        except Exception as e:
            print(f"   ❌ 服务启动失败: {e}")
            import traceback
            traceback.print_exc()
            return
        
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
