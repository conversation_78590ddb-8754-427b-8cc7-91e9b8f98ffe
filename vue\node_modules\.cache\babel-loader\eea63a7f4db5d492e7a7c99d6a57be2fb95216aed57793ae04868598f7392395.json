{"ast": null, "code": "import request from '@/utils/request.js';\nimport axios from 'axios';\n\n// 创建专门用于教案生成的axios实例\nconst lessonPlanRequest = axios.create({\n  baseURL: 'http://localhost:5300/api',\n  timeout: 60000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 生成教案\nexport const generateLessonPlanService = lessonPlanData => {\n  return lessonPlanRequest.post('/lesson-plan/generate', lessonPlanData).then(response => response.data).catch(error => {\n    console.error('教案生成API错误:', error);\n    throw error;\n  });\n};\n\n// 保存教案\nexport const saveLessonPlanService = lessonPlanData => {\n  return request.post('/lesson-plan/save', lessonPlanData);\n};\n\n// 获取教案列表\nexport const getLessonPlanListService = params => {\n  return request.get('/lesson-plan/list', {\n    params\n  });\n};\n\n// 获取教案详情\nexport const getLessonPlanDetailService = id => {\n  return request.get(`/lesson-plan/${id}`);\n};\n\n// 更新教案\nexport const updateLessonPlanService = (id, lessonPlanData) => {\n  return request.put(`/lesson-plan/${id}`, lessonPlanData);\n};\n\n// 删除教案\nexport const deleteLessonPlanService = id => {\n  return request.delete(`/lesson-plan/${id}`);\n};\n\n// 导出教案\nexport const exportLessonPlanService = (id, format = 'pdf') => {\n  return request.get(`/lesson-plan/${id}/export`, {\n    params: {\n      format\n    },\n    responseType: 'blob'\n  });\n};\n\n// 获取教案模板\nexport const getLessonPlanTemplatesService = () => {\n  return request.get('/lesson-plan/templates');\n};\n\n// AI智能优化教案\nexport const optimizeLessonPlanService = lessonPlanData => {\n  return request.post('/lesson-plan/optimize', lessonPlanData);\n};", "map": {"version": 3, "names": ["request", "axios", "lessonPlanRequest", "create", "baseURL", "timeout", "headers", "generateLessonPlanService", "lessonPlanData", "post", "then", "response", "data", "catch", "error", "console", "saveLessonPlanService", "getLessonPlanListService", "params", "get", "getLessonPlanDetailService", "id", "updateLessonPlanService", "put", "deleteLessonPlanService", "delete", "exportLessonPlanService", "format", "responseType", "getLessonPlanTemplatesService", "optimizeLessonPlanService"], "sources": ["D:/shishuo/vue/src/api/lessonPlan.js"], "sourcesContent": ["import request from '@/utils/request.js'\nimport axios from 'axios'\n\n// 创建专门用于教案生成的axios实例\nconst lessonPlanRequest = axios.create({\n  baseURL: 'http://localhost:5300/api',\n  timeout: 60000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n})\n\n// 生成教案\nexport const generateLessonPlanService = (lessonPlanData) => {\n  return lessonPlanRequest.post('/lesson-plan/generate', lessonPlanData)\n    .then(response => response.data)\n    .catch(error => {\n      console.error('教案生成API错误:', error)\n      throw error\n    })\n}\n\n// 保存教案\nexport const saveLessonPlanService = (lessonPlanData) => {\n  return request.post('/lesson-plan/save', lessonPlanData)\n}\n\n// 获取教案列表\nexport const getLessonPlanListService = (params) => {\n  return request.get('/lesson-plan/list', { params })\n}\n\n// 获取教案详情\nexport const getLessonPlanDetailService = (id) => {\n  return request.get(`/lesson-plan/${id}`)\n}\n\n// 更新教案\nexport const updateLessonPlanService = (id, lessonPlanData) => {\n  return request.put(`/lesson-plan/${id}`, lessonPlanData)\n}\n\n// 删除教案\nexport const deleteLessonPlanService = (id) => {\n  return request.delete(`/lesson-plan/${id}`)\n}\n\n// 导出教案\nexport const exportLessonPlanService = (id, format = 'pdf') => {\n  return request.get(`/lesson-plan/${id}/export`, {\n    params: { format },\n    responseType: 'blob'\n  })\n}\n\n// 获取教案模板\nexport const getLessonPlanTemplatesService = () => {\n  return request.get('/lesson-plan/templates')\n}\n\n// AI智能优化教案\nexport const optimizeLessonPlanService = (lessonPlanData) => {\n  return request.post('/lesson-plan/optimize', lessonPlanData)\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,iBAAiB,GAAGD,KAAK,CAACE,MAAM,CAAC;EACrCC,OAAO,EAAE,2BAA2B;EACpCC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,yBAAyB,GAAIC,cAAc,IAAK;EAC3D,OAAON,iBAAiB,CAACO,IAAI,CAAC,uBAAuB,EAAED,cAAc,CAAC,CACnEE,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAC/BC,KAAK,CAACC,KAAK,IAAI;IACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAME,qBAAqB,GAAIR,cAAc,IAAK;EACvD,OAAOR,OAAO,CAACS,IAAI,CAAC,mBAAmB,EAAED,cAAc,CAAC;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMS,wBAAwB,GAAIC,MAAM,IAAK;EAClD,OAAOlB,OAAO,CAACmB,GAAG,CAAC,mBAAmB,EAAE;IAAED;EAAO,CAAC,CAAC;AACrD,CAAC;;AAED;AACA,OAAO,MAAME,0BAA0B,GAAIC,EAAE,IAAK;EAChD,OAAOrB,OAAO,CAACmB,GAAG,CAAC,gBAAgBE,EAAE,EAAE,CAAC;AAC1C,CAAC;;AAED;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAACD,EAAE,EAAEb,cAAc,KAAK;EAC7D,OAAOR,OAAO,CAACuB,GAAG,CAAC,gBAAgBF,EAAE,EAAE,EAAEb,cAAc,CAAC;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMgB,uBAAuB,GAAIH,EAAE,IAAK;EAC7C,OAAOrB,OAAO,CAACyB,MAAM,CAAC,gBAAgBJ,EAAE,EAAE,CAAC;AAC7C,CAAC;;AAED;AACA,OAAO,MAAMK,uBAAuB,GAAGA,CAACL,EAAE,EAAEM,MAAM,GAAG,KAAK,KAAK;EAC7D,OAAO3B,OAAO,CAACmB,GAAG,CAAC,gBAAgBE,EAAE,SAAS,EAAE;IAC9CH,MAAM,EAAE;MAAES;IAAO,CAAC;IAClBC,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,6BAA6B,GAAGA,CAAA,KAAM;EACjD,OAAO7B,OAAO,CAACmB,GAAG,CAAC,wBAAwB,CAAC;AAC9C,CAAC;;AAED;AACA,OAAO,MAAMW,yBAAyB,GAAItB,cAAc,IAAK;EAC3D,OAAOR,OAAO,CAACS,IAAI,CAAC,uBAAuB,EAAED,cAAc,CAAC;AAC9D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}