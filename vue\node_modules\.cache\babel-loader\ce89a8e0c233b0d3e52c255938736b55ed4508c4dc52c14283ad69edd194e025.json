{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Document, Download, Plus, Delete, Edit, Loading } from '@element-plus/icons-vue';\nimport { generateLessonPlanService } from '@/api/lessonPlan.js';\n// import { saveLessonPlanService } from '@/api/lessonPlan.js' // 暂时注释，将来可能会用到\nimport LessonPlanPreview from '@/components/LessonPlanPreview.vue';\nexport default {\n  name: 'LessonPlanGenerator',\n  components: {\n    LessonPlanPreview\n  },\n  setup() {\n    // 表单数据\n    const formData = reactive({\n      subject: '',\n      // 学科\n      grade: '',\n      // 年级\n      topic: '',\n      // 课题\n      duration: 45,\n      // 课时长度（分钟）\n      objectives: [],\n      // 教学目标\n      keyPoints: '',\n      // 重点难点\n      materials: '',\n      // 教学材料\n      methods: [],\n      // 教学方法\n      steps: [],\n      // 教学步骤\n      homework: '',\n      // 作业布置\n      reflection: '' // 教学反思\n    });\n\n    // 教学目标列表\n    const objectiveInput = ref('');\n\n    // 教学方法选项\n    const methodOptions = [{\n      label: '讲授法',\n      value: 'lecture'\n    }, {\n      label: '讨论法',\n      value: 'discussion'\n    }, {\n      label: '实验法',\n      value: 'experiment'\n    }, {\n      label: '演示法',\n      value: 'demonstration'\n    }, {\n      label: '练习法',\n      value: 'practice'\n    }, {\n      label: '案例分析法',\n      value: 'case_study'\n    }, {\n      label: '小组合作',\n      value: 'group_work'\n    }, {\n      label: '多媒体教学',\n      value: 'multimedia'\n    }];\n\n    // 教学步骤\n    const stepInput = reactive({\n      title: '',\n      content: '',\n      time: 5\n    });\n\n    // 学科选项\n    const subjectOptions = ['语文', '数学', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '音乐', '美术', '体育', '信息技术'];\n\n    // 年级选项\n    const gradeOptions = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '七年级', '八年级', '九年级', '高一', '高二', '高三'];\n\n    // 添加教学目标\n    const addObjective = () => {\n      if (objectiveInput.value.trim()) {\n        formData.objectives.push(objectiveInput.value.trim());\n        objectiveInput.value = '';\n      }\n    };\n\n    // 删除教学目标\n    const removeObjective = index => {\n      formData.objectives.splice(index, 1);\n    };\n\n    // 添加教学步骤\n    const addStep = () => {\n      if (stepInput.title.trim() && stepInput.content.trim()) {\n        formData.steps.push({\n          title: stepInput.title,\n          content: stepInput.content,\n          time: stepInput.time\n        });\n        stepInput.title = '';\n        stepInput.content = '';\n        stepInput.time = 5;\n      }\n    };\n\n    // 删除教学步骤\n    const removeStep = index => {\n      formData.steps.splice(index, 1);\n    };\n\n    // 生成状态\n    const isGenerating = ref(false);\n\n    // 预览状态\n    const showPreview = ref(false);\n\n    // 生成教案\n    const generateLessonPlan = async () => {\n      // 验证必填字段\n      if (!formData.subject || !formData.grade || !formData.topic) {\n        ElMessage.error('请填写学科、年级和课题');\n        return;\n      }\n      if (formData.objectives.length === 0) {\n        ElMessage.error('请至少添加一个教学目标');\n        return;\n      }\n      if (formData.steps.length === 0) {\n        ElMessage.error('请至少添加一个教学步骤');\n        return;\n      }\n      try {\n        isGenerating.value = true;\n        ElMessage.info('教案生成中，请稍候...');\n\n        // 调用后端API生成教案\n        const result = await generateLessonPlanService(formData);\n        if (result.code === 0) {\n          ElMessage.success('教案生成成功！');\n\n          // 处理生成的教案数据\n          console.log('生成的教案:', result.data);\n\n          // 自动下载生成的Word文档\n          if (result.data.filename) {\n            await downloadLessonPlan(result.data.filename);\n          }\n\n          // 如果有AI增强内容，可以显示给用户\n          if (result.data.enhanced_content) {\n            console.log('AI增强内容:', result.data.enhanced_content);\n          }\n        } else {\n          ElMessage.error(result.message || '教案生成失败');\n        }\n      } catch (error) {\n        console.error('教案生成错误:', error);\n        ElMessage.error('教案生成失败：' + (error.message || '网络错误'));\n      } finally {\n        isGenerating.value = false;\n      }\n    };\n\n    // 下载教案文件\n    const downloadLessonPlan = async filename => {\n      try {\n        // 直接使用浏览器下载\n        const downloadUrl = `http://localhost:5300/api/lesson-plan/download/${encodeURIComponent(filename)}`;\n\n        // 创建下载链接\n        const link = document.createElement('a');\n        link.href = downloadUrl;\n        link.download = filename;\n        link.target = '_blank';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        ElMessage.success('教案下载成功！');\n      } catch (error) {\n        console.error('下载教案失败:', error);\n        ElMessage.error('下载教案失败：' + (error.message || '网络错误'));\n      }\n    };\n\n    // 重置表单\n    const resetForm = () => {\n      ElMessageBox.confirm('确认重置所有内容吗？', '提示', {\n        confirmButtonText: '确认',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        Object.assign(formData, {\n          subject: '',\n          grade: '',\n          topic: '',\n          duration: 45,\n          objectives: [],\n          keyPoints: '',\n          materials: '',\n          methods: [],\n          steps: [],\n          homework: '',\n          reflection: ''\n        });\n        objectiveInput.value = '';\n        ElMessage.success('表单已重置');\n      });\n    };\n\n    // 预览教案\n    const previewLessonPlan = () => {\n      // 验证基本信息\n      if (!formData.subject || !formData.grade || !formData.topic) {\n        ElMessage.error('请先填写基本信息（学科、年级、课题）');\n        return;\n      }\n      showPreview.value = true;\n    };\n\n    // 导出教案\n    const handleExport = format => {\n      ElMessage.info(`正在导出${format.toUpperCase()}格式...`);\n      // 这里可以调用导出API\n    };\n\n    // 打印教案\n    const handlePrint = () => {\n      window.print();\n    };\n    return {\n      formData,\n      objectiveInput,\n      methodOptions,\n      stepInput,\n      subjectOptions,\n      gradeOptions,\n      isGenerating,\n      showPreview,\n      addObjective,\n      removeObjective,\n      addStep,\n      removeStep,\n      generateLessonPlan,\n      downloadLessonPlan,\n      resetForm,\n      previewLessonPlan,\n      handleExport,\n      handlePrint,\n      // 图标\n      Document,\n      Download,\n      Plus,\n      Delete,\n      Edit,\n      Loading\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "ElMessage", "ElMessageBox", "Document", "Download", "Plus", "Delete", "Edit", "Loading", "generateLessonPlanService", "LessonPlanPreview", "name", "components", "setup", "formData", "subject", "grade", "topic", "duration", "objectives", "keyPoints", "materials", "methods", "steps", "homework", "reflection", "objectiveInput", "methodOptions", "label", "value", "stepInput", "title", "content", "time", "subjectOptions", "gradeOptions", "addObjective", "trim", "push", "removeObjective", "index", "splice", "addStep", "removeStep", "isGenerating", "showPreview", "generateLessonPlan", "error", "length", "info", "result", "code", "success", "console", "log", "data", "filename", "downloadLessonPlan", "enhanced_content", "message", "downloadUrl", "encodeURIComponent", "link", "document", "createElement", "href", "download", "target", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "resetForm", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "Object", "assign", "previewLessonPlan", "handleExport", "format", "toUpperCase", "handlePrint", "window", "print"], "sources": ["D:\\shishuo\\vue\\src\\views\\LessonPlanGenerator.vue"], "sourcesContent": ["<script>\nimport { ref, reactive } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Document, Download, Plus, Delete, Edit, Loading } from '@element-plus/icons-vue'\nimport { generateLessonPlanService } from '@/api/lessonPlan.js'\n// import { saveLessonPlanService } from '@/api/lessonPlan.js' // 暂时注释，将来可能会用到\nimport LessonPlanPreview from '@/components/LessonPlanPreview.vue'\n\nexport default {\n  name: 'LessonPlanGenerator',\n  components: {\n    LessonPlanPreview\n  },\n  setup() {\n    // 表单数据\n    const formData = reactive({\n      subject: '', // 学科\n      grade: '', // 年级\n      topic: '', // 课题\n      duration: 45, // 课时长度（分钟）\n      objectives: [], // 教学目标\n      keyPoints: '', // 重点难点\n      materials: '', // 教学材料\n      methods: [], // 教学方法\n      steps: [], // 教学步骤\n      homework: '', // 作业布置\n      reflection: '' // 教学反思\n    })\n\n    // 教学目标列表\n    const objectiveInput = ref('')\n\n    // 教学方法选项\n    const methodOptions = [\n      { label: '讲授法', value: 'lecture' },\n      { label: '讨论法', value: 'discussion' },\n      { label: '实验法', value: 'experiment' },\n      { label: '演示法', value: 'demonstration' },\n      { label: '练习法', value: 'practice' },\n      { label: '案例分析法', value: 'case_study' },\n      { label: '小组合作', value: 'group_work' },\n      { label: '多媒体教学', value: 'multimedia' }\n    ]\n\n    // 教学步骤\n    const stepInput = reactive({\n      title: '',\n      content: '',\n      time: 5\n    })\n\n    // 学科选项\n    const subjectOptions = [\n      '语文', '数学', '英语', '物理', '化学', '生物',\n      '历史', '地理', '政治', '音乐', '美术', '体育', '信息技术'\n    ]\n\n    // 年级选项\n    const gradeOptions = [\n      '一年级', '二年级', '三年级', '四年级', '五年级', '六年级',\n      '七年级', '八年级', '九年级', '高一', '高二', '高三'\n    ]\n\n    // 添加教学目标\n    const addObjective = () => {\n      if (objectiveInput.value.trim()) {\n        formData.objectives.push(objectiveInput.value.trim())\n        objectiveInput.value = ''\n      }\n    }\n\n    // 删除教学目标\n    const removeObjective = (index) => {\n      formData.objectives.splice(index, 1)\n    }\n\n    // 添加教学步骤\n    const addStep = () => {\n      if (stepInput.title.trim() && stepInput.content.trim()) {\n        formData.steps.push({\n          title: stepInput.title,\n          content: stepInput.content,\n          time: stepInput.time\n        })\n        stepInput.title = ''\n        stepInput.content = ''\n        stepInput.time = 5\n      }\n    }\n\n    // 删除教学步骤\n    const removeStep = (index) => {\n      formData.steps.splice(index, 1)\n    }\n\n    // 生成状态\n    const isGenerating = ref(false)\n\n    // 预览状态\n    const showPreview = ref(false)\n\n    // 生成教案\n    const generateLessonPlan = async () => {\n      // 验证必填字段\n      if (!formData.subject || !formData.grade || !formData.topic) {\n        ElMessage.error('请填写学科、年级和课题')\n        return\n      }\n\n      if (formData.objectives.length === 0) {\n        ElMessage.error('请至少添加一个教学目标')\n        return\n      }\n\n      if (formData.steps.length === 0) {\n        ElMessage.error('请至少添加一个教学步骤')\n        return\n      }\n\n      try {\n        isGenerating.value = true\n        ElMessage.info('教案生成中，请稍候...')\n\n        // 调用后端API生成教案\n        const result = await generateLessonPlanService(formData)\n\n        if (result.code === 0) {\n          ElMessage.success('教案生成成功！')\n\n          // 处理生成的教案数据\n          console.log('生成的教案:', result.data)\n\n          // 自动下载生成的Word文档\n          if (result.data.filename) {\n            await downloadLessonPlan(result.data.filename)\n          }\n\n          // 如果有AI增强内容，可以显示给用户\n          if (result.data.enhanced_content) {\n            console.log('AI增强内容:', result.data.enhanced_content)\n          }\n\n        } else {\n          ElMessage.error(result.message || '教案生成失败')\n        }\n\n      } catch (error) {\n        console.error('教案生成错误:', error)\n        ElMessage.error('教案生成失败：' + (error.message || '网络错误'))\n      } finally {\n        isGenerating.value = false\n      }\n    }\n\n    // 下载教案文件\n    const downloadLessonPlan = async (filename) => {\n      try {\n        // 直接使用浏览器下载\n        const downloadUrl = `http://localhost:5300/api/lesson-plan/download/${encodeURIComponent(filename)}`\n\n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = downloadUrl\n        link.download = filename\n        link.target = '_blank'\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n\n        ElMessage.success('教案下载成功！')\n\n      } catch (error) {\n        console.error('下载教案失败:', error)\n        ElMessage.error('下载教案失败：' + (error.message || '网络错误'))\n      }\n    }\n\n    // 重置表单\n    const resetForm = () => {\n      ElMessageBox.confirm('确认重置所有内容吗？', '提示', {\n        confirmButtonText: '确认',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        Object.assign(formData, {\n          subject: '',\n          grade: '',\n          topic: '',\n          duration: 45,\n          objectives: [],\n          keyPoints: '',\n          materials: '',\n          methods: [],\n          steps: [],\n          homework: '',\n          reflection: ''\n        })\n        objectiveInput.value = ''\n        ElMessage.success('表单已重置')\n      })\n    }\n\n    // 预览教案\n    const previewLessonPlan = () => {\n      // 验证基本信息\n      if (!formData.subject || !formData.grade || !formData.topic) {\n        ElMessage.error('请先填写基本信息（学科、年级、课题）')\n        return\n      }\n\n      showPreview.value = true\n    }\n\n    // 导出教案\n    const handleExport = (format) => {\n      ElMessage.info(`正在导出${format.toUpperCase()}格式...`)\n      // 这里可以调用导出API\n    }\n\n    // 打印教案\n    const handlePrint = () => {\n      window.print()\n    }\n\n    return {\n      formData,\n      objectiveInput,\n      methodOptions,\n      stepInput,\n      subjectOptions,\n      gradeOptions,\n      isGenerating,\n      showPreview,\n      addObjective,\n      removeObjective,\n      addStep,\n      removeStep,\n      generateLessonPlan,\n      downloadLessonPlan,\n      resetForm,\n      previewLessonPlan,\n      handleExport,\n      handlePrint,\n      // 图标\n      Document,\n      Download,\n      Plus,\n      Delete,\n      Edit,\n      Loading\n    }\n  }\n}\n</script>\n\n<template>\n  <el-card class=\"lesson-plan-container\">\n    <template #header>\n      <div class=\"header\">\n        <span class=\"title\">\n          <el-icon><Document /></el-icon>\n          智能教案生成器\n        </span>\n        <div class=\"actions\">\n          <el-button type=\"info\" @click=\"previewLessonPlan\">\n            <el-icon><Edit /></el-icon>\n            预览\n          </el-button>\n          <el-button type=\"warning\" @click=\"resetForm\">\n            重置\n          </el-button>\n          <el-button\n            type=\"primary\"\n            @click=\"generateLessonPlan\"\n            :loading=\"isGenerating\"\n            :disabled=\"isGenerating\"\n          >\n            <el-icon v-if=\"!isGenerating\"><Download /></el-icon>\n            <el-icon v-else><Loading /></el-icon>\n            {{ isGenerating ? '生成中...' : '生成Word教案' }}\n          </el-button>\n        </div>\n      </div>\n    </template>\n\n    <el-form :model=\"formData\" label-width=\"120px\" class=\"lesson-form\">\n      <!-- 基本信息 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>基本信息</h3>\n        </template>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"学科\" required>\n              <el-select v-model=\"formData.subject\" placeholder=\"请选择学科\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"subject in subjectOptions\"\n                  :key=\"subject\"\n                  :label=\"subject\"\n                  :value=\"subject\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"年级\" required>\n              <el-select v-model=\"formData.grade\" placeholder=\"请选择年级\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"grade in gradeOptions\"\n                  :key=\"grade\"\n                  :label=\"grade\"\n                  :value=\"grade\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"课时长度\">\n              <el-input-number\n                v-model=\"formData.duration\"\n                :min=\"10\"\n                :max=\"120\"\n                :step=\"5\"\n                style=\"width: 100%\"\n              />\n              <span style=\"margin-left: 8px;\">分钟</span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-form-item label=\"课题\" required>\n          <el-input\n            v-model=\"formData.topic\"\n            placeholder=\"请输入课题名称\"\n            maxlength=\"100\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-card>\n\n      <!-- 教学目标 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>教学目标</h3>\n        </template>\n\n        <div class=\"objective-input\">\n          <el-input\n            v-model=\"objectiveInput\"\n            placeholder=\"请输入教学目标\"\n            @keyup.enter=\"addObjective\"\n          >\n            <template #append>\n              <el-button @click=\"addObjective\" :icon=\"Plus\">添加</el-button>\n            </template>\n          </el-input>\n        </div>\n\n        <div class=\"objectives-list\" v-if=\"formData.objectives.length > 0\">\n          <el-tag\n            v-for=\"(objective, index) in formData.objectives\"\n            :key=\"index\"\n            closable\n            @close=\"removeObjective(index)\"\n            class=\"objective-tag\"\n          >\n            {{ objective }}\n          </el-tag>\n        </div>\n      </el-card>\n\n      <!-- 重点难点 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>重点难点</h3>\n        </template>\n\n        <el-form-item label=\"教学重点\">\n          <el-input\n            v-model=\"formData.keyPoints\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请描述本课的教学重点和难点\"\n            maxlength=\"500\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-card>\n\n      <!-- 教学方法 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>教学方法</h3>\n        </template>\n\n        <el-form-item label=\"教学方法\">\n          <el-checkbox-group v-model=\"formData.methods\">\n            <el-checkbox\n              v-for=\"method in methodOptions\"\n              :key=\"method.value\"\n              :label=\"method.value\"\n            >\n              {{ method.label }}\n            </el-checkbox>\n          </el-checkbox-group>\n        </el-form-item>\n\n        <el-form-item label=\"教学材料\">\n          <el-input\n            v-model=\"formData.materials\"\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请列出所需的教学材料和设备\"\n            maxlength=\"300\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-card>\n\n      <!-- 教学步骤 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>教学步骤</h3>\n        </template>\n\n        <div class=\"step-input\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-input\n                v-model=\"stepInput.title\"\n                placeholder=\"步骤标题\"\n                maxlength=\"50\"\n              />\n            </el-col>\n            <el-col :span=\"12\">\n              <el-input\n                v-model=\"stepInput.content\"\n                placeholder=\"步骤内容\"\n                maxlength=\"200\"\n              />\n            </el-col>\n            <el-col :span=\"4\">\n              <el-input-number\n                v-model=\"stepInput.time\"\n                :min=\"1\"\n                :max=\"60\"\n                placeholder=\"时长\"\n                style=\"width: 100%\"\n              />\n            </el-col>\n            <el-col :span=\"2\">\n              <el-button @click=\"addStep\" :icon=\"Plus\" type=\"primary\">添加</el-button>\n            </el-col>\n          </el-row>\n        </div>\n\n        <div class=\"steps-list\" v-if=\"formData.steps.length > 0\">\n          <el-timeline>\n            <el-timeline-item\n              v-for=\"(step, index) in formData.steps\"\n              :key=\"index\"\n              :timestamp=\"`${step.time}分钟`\"\n            >\n              <el-card class=\"step-card\">\n                <div class=\"step-header\">\n                  <h4>{{ step.title }}</h4>\n                  <el-button\n                    @click=\"removeStep(index)\"\n                    :icon=\"Delete\"\n                    size=\"small\"\n                    type=\"danger\"\n                    text\n                  />\n                </div>\n                <p>{{ step.content }}</p>\n              </el-card>\n            </el-timeline-item>\n          </el-timeline>\n        </div>\n      </el-card>\n\n      <!-- 作业与反思 -->\n      <el-card class=\"form-section\" shadow=\"never\">\n        <template #header>\n          <h3>作业与反思</h3>\n        </template>\n\n        <el-form-item label=\"作业布置\">\n          <el-input\n            v-model=\"formData.homework\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请描述课后作业安排\"\n            maxlength=\"300\"\n            show-word-limit\n          />\n        </el-form-item>\n\n        <el-form-item label=\"教学反思\">\n          <el-input\n            v-model=\"formData.reflection\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请填写教学反思（可在课后补充）\"\n            maxlength=\"500\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-card>\n    </el-form>\n\n    <!-- 教案预览组件 -->\n    <LessonPlanPreview\n      v-model:visible=\"showPreview\"\n      :lesson-plan=\"formData\"\n      @export=\"handleExport\"\n      @print=\"handlePrint\"\n    />\n  </el-card>\n</template>\n\n<style lang=\"scss\" scoped>\n.lesson-plan-container {\n  margin: 20px;\n\n  .header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .title {\n      font-size: 18px;\n      font-weight: bold;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      color: #409eff;\n    }\n\n    .actions {\n      display: flex;\n      gap: 10px;\n    }\n  }\n}\n\n.lesson-form {\n  .form-section {\n    margin-bottom: 20px;\n    border: 1px solid #e4e7ed;\n\n    h3 {\n      margin: 0;\n      color: #409eff;\n      font-size: 16px;\n    }\n  }\n\n  .objective-input {\n    margin-bottom: 15px;\n  }\n\n  .objectives-list {\n    .objective-tag {\n      margin: 5px 5px 5px 0;\n      padding: 8px 12px;\n      font-size: 14px;\n      background-color: #f0f9ff;\n      border-color: #409eff;\n    }\n  }\n\n  .step-input {\n    margin-bottom: 20px;\n    padding: 15px;\n    background-color: #f8f9fa;\n    border-radius: 6px;\n  }\n\n  .steps-list {\n    .step-card {\n      margin-bottom: 10px;\n\n      .step-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 10px;\n\n        h4 {\n          margin: 0;\n          color: #303133;\n          font-size: 14px;\n        }\n      }\n\n      p {\n        margin: 0;\n        color: #606266;\n        line-height: 1.6;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .lesson-plan-container {\n    margin: 10px;\n\n    .header {\n      flex-direction: column;\n      gap: 15px;\n\n      .actions {\n        width: 100%;\n        justify-content: center;\n      }\n    }\n  }\n\n  .step-input {\n    .el-row {\n      .el-col {\n        margin-bottom: 10px;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";AACA,SAASA,GAAG,EAAEC,QAAO,QAAS,KAAI;AAClC,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAM,QAAS,yBAAwB;AACxF,SAASC,yBAAwB,QAAS,qBAAoB;AAC9D;AACA,OAAOC,iBAAgB,MAAO,oCAAmC;AAEjE,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN;IACA,MAAMC,QAAO,GAAId,QAAQ,CAAC;MACxBe,OAAO,EAAE,EAAE;MAAE;MACbC,KAAK,EAAE,EAAE;MAAE;MACXC,KAAK,EAAE,EAAE;MAAE;MACXC,QAAQ,EAAE,EAAE;MAAE;MACdC,UAAU,EAAE,EAAE;MAAE;MAChBC,SAAS,EAAE,EAAE;MAAE;MACfC,SAAS,EAAE,EAAE;MAAE;MACfC,OAAO,EAAE,EAAE;MAAE;MACbC,KAAK,EAAE,EAAE;MAAE;MACXC,QAAQ,EAAE,EAAE;MAAE;MACdC,UAAU,EAAE,EAAC,CAAE;IACjB,CAAC;;IAED;IACA,MAAMC,cAAa,GAAI3B,GAAG,CAAC,EAAE;;IAE7B;IACA,MAAM4B,aAAY,GAAI,CACpB;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAU,CAAC,EAClC;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAa,CAAC,EACrC;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAa,CAAC,EACrC;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAgB,CAAC,EACxC;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAW,CAAC,EACnC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAa,CAAC,EACvC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAa,CAAC,EACtC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAa,EACxC;;IAEA;IACA,MAAMC,SAAQ,GAAI9B,QAAQ,CAAC;MACzB+B,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE;IACR,CAAC;;IAED;IACA,MAAMC,cAAa,GAAI,CACrB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAClC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAK,CAC3C;;IAEA;IACA,MAAMC,YAAW,GAAI,CACnB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACxC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAG,CACtC;;IAEA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIV,cAAc,CAACG,KAAK,CAACQ,IAAI,CAAC,CAAC,EAAE;QAC/BvB,QAAQ,CAACK,UAAU,CAACmB,IAAI,CAACZ,cAAc,CAACG,KAAK,CAACQ,IAAI,CAAC,CAAC;QACpDX,cAAc,CAACG,KAAI,GAAI,EAAC;MAC1B;IACF;;IAEA;IACA,MAAMU,eAAc,GAAKC,KAAK,IAAK;MACjC1B,QAAQ,CAACK,UAAU,CAACsB,MAAM,CAACD,KAAK,EAAE,CAAC;IACrC;;IAEA;IACA,MAAME,OAAM,GAAIA,CAAA,KAAM;MACpB,IAAIZ,SAAS,CAACC,KAAK,CAACM,IAAI,CAAC,KAAKP,SAAS,CAACE,OAAO,CAACK,IAAI,CAAC,CAAC,EAAE;QACtDvB,QAAQ,CAACS,KAAK,CAACe,IAAI,CAAC;UAClBP,KAAK,EAAED,SAAS,CAACC,KAAK;UACtBC,OAAO,EAAEF,SAAS,CAACE,OAAO;UAC1BC,IAAI,EAAEH,SAAS,CAACG;QAClB,CAAC;QACDH,SAAS,CAACC,KAAI,GAAI,EAAC;QACnBD,SAAS,CAACE,OAAM,GAAI,EAAC;QACrBF,SAAS,CAACG,IAAG,GAAI;MACnB;IACF;;IAEA;IACA,MAAMU,UAAS,GAAKH,KAAK,IAAK;MAC5B1B,QAAQ,CAACS,KAAK,CAACkB,MAAM,CAACD,KAAK,EAAE,CAAC;IAChC;;IAEA;IACA,MAAMI,YAAW,GAAI7C,GAAG,CAAC,KAAK;;IAE9B;IACA,MAAM8C,WAAU,GAAI9C,GAAG,CAAC,KAAK;;IAE7B;IACA,MAAM+C,kBAAiB,GAAI,MAAAA,CAAA,KAAY;MACrC;MACA,IAAI,CAAChC,QAAQ,CAACC,OAAM,IAAK,CAACD,QAAQ,CAACE,KAAI,IAAK,CAACF,QAAQ,CAACG,KAAK,EAAE;QAC3DhB,SAAS,CAAC8C,KAAK,CAAC,aAAa;QAC7B;MACF;MAEA,IAAIjC,QAAQ,CAACK,UAAU,CAAC6B,MAAK,KAAM,CAAC,EAAE;QACpC/C,SAAS,CAAC8C,KAAK,CAAC,aAAa;QAC7B;MACF;MAEA,IAAIjC,QAAQ,CAACS,KAAK,CAACyB,MAAK,KAAM,CAAC,EAAE;QAC/B/C,SAAS,CAAC8C,KAAK,CAAC,aAAa;QAC7B;MACF;MAEA,IAAI;QACFH,YAAY,CAACf,KAAI,GAAI,IAAG;QACxB5B,SAAS,CAACgD,IAAI,CAAC,cAAc;;QAE7B;QACA,MAAMC,MAAK,GAAI,MAAMzC,yBAAyB,CAACK,QAAQ;QAEvD,IAAIoC,MAAM,CAACC,IAAG,KAAM,CAAC,EAAE;UACrBlD,SAAS,CAACmD,OAAO,CAAC,SAAS;;UAE3B;UACAC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEJ,MAAM,CAACK,IAAI;;UAEjC;UACA,IAAIL,MAAM,CAACK,IAAI,CAACC,QAAQ,EAAE;YACxB,MAAMC,kBAAkB,CAACP,MAAM,CAACK,IAAI,CAACC,QAAQ;UAC/C;;UAEA;UACA,IAAIN,MAAM,CAACK,IAAI,CAACG,gBAAgB,EAAE;YAChCL,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEJ,MAAM,CAACK,IAAI,CAACG,gBAAgB;UACrD;QAEF,OAAO;UACLzD,SAAS,CAAC8C,KAAK,CAACG,MAAM,CAACS,OAAM,IAAK,QAAQ;QAC5C;MAEF,EAAE,OAAOZ,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B9C,SAAS,CAAC8C,KAAK,CAAC,SAAQ,IAAKA,KAAK,CAACY,OAAM,IAAK,MAAM,CAAC;MACvD,UAAU;QACRf,YAAY,CAACf,KAAI,GAAI,KAAI;MAC3B;IACF;;IAEA;IACA,MAAM4B,kBAAiB,GAAI,MAAOD,QAAQ,IAAK;MAC7C,IAAI;QACF;QACA,MAAMI,WAAU,GAAI,kDAAkDC,kBAAkB,CAACL,QAAQ,CAAC,EAAC;;QAEnG;QACA,MAAMM,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG;QACvCF,IAAI,CAACG,IAAG,GAAIL,WAAU;QACtBE,IAAI,CAACI,QAAO,GAAIV,QAAO;QACvBM,IAAI,CAACK,MAAK,GAAI,QAAO;QACrBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI;QAC9BA,IAAI,CAACQ,KAAK,CAAC;QACXP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI;QAE9B7D,SAAS,CAACmD,OAAO,CAAC,SAAS;MAE7B,EAAE,OAAOL,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B9C,SAAS,CAAC8C,KAAK,CAAC,SAAQ,IAAKA,KAAK,CAACY,OAAM,IAAK,MAAM,CAAC;MACvD;IACF;;IAEA;IACA,MAAMa,SAAQ,GAAIA,CAAA,KAAM;MACtBtE,YAAY,CAACuE,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE;QACvCC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACZC,MAAM,CAACC,MAAM,CAACjE,QAAQ,EAAE;UACtBC,OAAO,EAAE,EAAE;UACXC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,EAAE;UACdC,SAAS,EAAE,EAAE;UACbC,SAAS,EAAE,EAAE;UACbC,OAAO,EAAE,EAAE;UACXC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE;QACd,CAAC;QACDC,cAAc,CAACG,KAAI,GAAI,EAAC;QACxB5B,SAAS,CAACmD,OAAO,CAAC,OAAO;MAC3B,CAAC;IACH;;IAEA;IACA,MAAM4B,iBAAgB,GAAIA,CAAA,KAAM;MAC9B;MACA,IAAI,CAAClE,QAAQ,CAACC,OAAM,IAAK,CAACD,QAAQ,CAACE,KAAI,IAAK,CAACF,QAAQ,CAACG,KAAK,EAAE;QAC3DhB,SAAS,CAAC8C,KAAK,CAAC,oBAAoB;QACpC;MACF;MAEAF,WAAW,CAAChB,KAAI,GAAI,IAAG;IACzB;;IAEA;IACA,MAAMoD,YAAW,GAAKC,MAAM,IAAK;MAC/BjF,SAAS,CAACgD,IAAI,CAAC,OAAOiC,MAAM,CAACC,WAAW,CAAC,CAAC,OAAO;MACjD;IACF;;IAEA;IACA,MAAMC,WAAU,GAAIA,CAAA,KAAM;MACxBC,MAAM,CAACC,KAAK,CAAC;IACf;IAEA,OAAO;MACLxE,QAAQ;MACRY,cAAc;MACdC,aAAa;MACbG,SAAS;MACTI,cAAc;MACdC,YAAY;MACZS,YAAY;MACZC,WAAW;MACXT,YAAY;MACZG,eAAe;MACfG,OAAO;MACPC,UAAU;MACVG,kBAAkB;MAClBW,kBAAkB;MAClBe,SAAS;MACTQ,iBAAiB;MACjBC,YAAY;MACZG,WAAW;MACX;MACAjF,QAAQ;MACRC,QAAQ;MACRC,IAAI;MACJC,MAAM;MACNC,IAAI;MACJC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}