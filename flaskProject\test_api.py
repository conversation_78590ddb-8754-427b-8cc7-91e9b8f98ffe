#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

# 测试数据
test_data = {
    "subject": "语文",
    "grade": "三年级",
    "topic": "古诗词欣赏",
    "duration": 40,
    "objectives": [
        "理解古诗词的基本含义",
        "学会朗读古诗词",
        "感受古诗词的韵律美"
    ],
    "keyPoints": "古诗词的理解和朗读技巧",
    "materials": "古诗词卡片、音频设备、多媒体课件",
    "methods": ["lecture", "discussion", "multimedia"],
    "steps": [
        {
            "title": "诗词导入",
            "content": "播放古诗词音频，激发学习兴趣",
            "time": 8
        },
        {
            "title": "诗词学习",
            "content": "逐句讲解古诗词的含义",
            "time": 20
        },
        {
            "title": "朗读练习",
            "content": "学生练习朗读，体会韵律",
            "time": 12
        }
    ],
    "homework": "背诵今天学习的古诗词",
    "reflection": "观察学生的学习反应"
}

print("测试教案生成API...")
print(f"测试数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")

try:
    # 发送POST请求到教案生成API
    url = "http://localhost:5300/api/lesson-plan/generate"
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"发送请求到: {url}")
    response = requests.post(url, json=test_data, headers=headers)
    
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        print("✓ API调用成功！")
        print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get("code") == 0:
            filename = result.get("data", {}).get("filename")
            if filename:
                print(f"✓ 教案生成成功！文件名: {filename}")
                
                # 测试文件下载
                download_url = f"http://localhost:5300/api/lesson-plan/download/{filename}"
                print(f"测试文件下载: {download_url}")
                
                download_response = requests.get(download_url)
                print(f"下载响应状态码: {download_response.status_code}")
                
                if download_response.status_code == 200:
                    print("✓ 文件下载成功！")
                    print(f"文件大小: {len(download_response.content)} 字节")
                else:
                    print(f"✗ 文件下载失败: {download_response.text}")
            else:
                print("✗ 响应中没有文件名")
        else:
            print(f"✗ API返回错误: {result.get('message')}")
    else:
        print(f"✗ API调用失败，状态码: {response.status_code}")
        print(f"错误信息: {response.text}")
        
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()
