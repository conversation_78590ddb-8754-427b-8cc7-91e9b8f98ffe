import os
import uuid
import logging
from typing import Dict, Any
from openai import OpenAI
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
from config import config

logger = logging.getLogger(__name__)

class LessonPlanService:
    def __init__(self, api_key: str, base_url: str, upload_folder: str):
        """
        初始化教案服务
        :param api_key: API密钥
        :param base_url: API基础URL
        :param upload_folder: 文件上传目录
        """
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        self.upload_folder = upload_folder
        os.makedirs(upload_folder, exist_ok=True)

    def generate_lesson_plan_word(self, lesson_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成Word格式的智能教案
        
        Args:
            lesson_data: 教案数据，包含学科、年级、课题等信息
            
        Returns:
            包含文件路径和文件名的字典或错误信息
        """
        try:
            # 使用AI优化和完善教案内容
            enhanced_content = self._enhance_lesson_content(lesson_data)
            
            # 创建Word文档
            doc_path = self._create_word_document(enhanced_content)
            
            logger.info(f"教案生成成功：{doc_path}")
            
            # 返回包含文件路径和文件名的字典
            filename = os.path.basename(doc_path)
            return {
                "file_path": doc_path,
                "filename": filename,
                "status": "success",
                "enhanced_content": enhanced_content
            }
            
        except Exception as e:
            logger.error(f"生成教案失败: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "status": "error"
            }

    def _enhance_lesson_content(self, lesson_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用AI增强和完善教案内容
        """
        try:
            # 构建AI提示词
            system_prompt = """你是一个专业的教学设计专家。请根据用户提供的教案基础信息，生成一份完整、专业的教案内容。

请按照以下结构生成教案：
1. 教学目标（知识目标、能力目标、情感目标）
2. 教学重点难点
3. 教学方法和手段
4. 教学准备（教具、学具、多媒体等）
5. 教学过程（详细的教学步骤，包括时间分配）
6. 板书设计
7. 作业布置
8. 教学反思

要求：
- 内容要符合教育教学规律
- 教学目标要具体、可测量
- 教学过程要详细、可操作
- 语言要专业、规范
- 适合指定的年级和学科特点
"""

            # 构建用户输入
            user_content = f"""
学科：{lesson_data.get('subject', '')}
年级：{lesson_data.get('grade', '')}
课题：{lesson_data.get('topic', '')}
课时长度：{lesson_data.get('duration', 45)}分钟
教学目标：{', '.join(lesson_data.get('objectives', []))}
重点难点：{lesson_data.get('keyPoints', '')}
教学材料：{lesson_data.get('materials', '')}
教学方法：{', '.join([self._get_method_label(m) for m in lesson_data.get('methods', [])])}
教学步骤：{self._format_steps(lesson_data.get('steps', []))}
作业布置：{lesson_data.get('homework', '')}
教学反思：{lesson_data.get('reflection', '')}
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ]

            # 调用AI生成内容，设置较短的超时时间
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=messages,
                temperature=0.7,
                max_tokens=2000,
                timeout=30  # 30秒超时
            )

            ai_content = response.choices[0].message.content

            # 解析AI生成的内容并与原始数据合并
            enhanced_data = lesson_data.copy()
            enhanced_data['ai_enhanced_content'] = ai_content

            return enhanced_data

        except Exception as e:
            logger.error(f"AI增强内容失败: {str(e)}")
            # 如果AI增强失败，返回原始数据并添加默认的AI内容
            enhanced_data = lesson_data.copy()
            enhanced_data['ai_enhanced_content'] = self._generate_default_content(lesson_data)
            return enhanced_data

    def _generate_default_content(self, lesson_data: Dict[str, Any]) -> str:
        """
        生成默认的教案内容（当AI调用失败时使用）
        """
        subject = lesson_data.get('subject', '')
        grade = lesson_data.get('grade', '')
        topic = lesson_data.get('topic', '')

        return f"""
**{grade}{subject}教案：{topic}**

### 一、教学目标
{chr(10).join([f"- {obj}" for obj in lesson_data.get('objectives', [])])}

### 二、教学重点难点
{lesson_data.get('keyPoints', '待完善')}

### 三、教学方法
{', '.join([self._get_method_label(m) for m in lesson_data.get('methods', [])])}

### 四、教学准备
{lesson_data.get('materials', '待完善')}

### 五、教学过程
{chr(10).join([f"{i+1}. {step.get('title', '')}（{step.get('time', 0)}分钟）：{step.get('content', '')}" for i, step in enumerate(lesson_data.get('steps', []))])}

### 六、作业布置
{lesson_data.get('homework', '待完善')}

### 七、教学反思
{lesson_data.get('reflection', '待课后补充')}
"""

    def _get_method_label(self, method: str) -> str:
        """获取教学方法的中文标签"""
        method_labels = {
            'lecture': '讲授法',
            'discussion': '讨论法',
            'experiment': '实验法',
            'demonstration': '演示法',
            'practice': '练习法',
            'case_study': '案例分析法',
            'group_work': '小组合作',
            'multimedia': '多媒体教学'
        }
        return method_labels.get(method, method)

    def _format_steps(self, steps: list) -> str:
        """格式化教学步骤"""
        if not steps:
            return ""
        
        formatted = []
        for i, step in enumerate(steps, 1):
            formatted.append(f"{i}. {step.get('title', '')}（{step.get('time', 0)}分钟）：{step.get('content', '')}")
        
        return "\n".join(formatted)

    def _create_word_document(self, lesson_data: Dict[str, Any]) -> str:
        """
        创建Word文档
        """
        # 生成唯一文件名
        file_id = uuid.uuid4().hex
        topic = lesson_data.get('topic', '教案')
        base_name = f"{topic}_{file_id}"
        doc_path = os.path.join(self.upload_folder, f"{base_name}.docx")

        # 创建文档
        doc = Document()
        
        # 设置文档样式
        self._setup_document_styles(doc)
        
        # 添加标题
        title = doc.add_heading(f"{lesson_data.get('topic', '教案')}", 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加基本信息表格
        self._add_basic_info_table(doc, lesson_data)
        
        # 添加教学目标
        self._add_teaching_objectives(doc, lesson_data)
        
        # 添加重点难点
        self._add_key_points(doc, lesson_data)
        
        # 添加教学方法
        self._add_teaching_methods(doc, lesson_data)
        
        # 添加教学准备
        self._add_teaching_materials(doc, lesson_data)
        
        # 添加教学过程
        self._add_teaching_process(doc, lesson_data)
        
        # 添加作业布置
        self._add_homework(doc, lesson_data)
        
        # 添加教学反思
        self._add_reflection(doc, lesson_data)
        
        # 如果有AI增强内容，添加到文档末尾
        if 'ai_enhanced_content' in lesson_data:
            self._add_ai_enhanced_content(doc, lesson_data['ai_enhanced_content'])
        
        # 保存文档
        doc.save(doc_path)
        
        return doc_path

    def _setup_document_styles(self, doc):
        """设置文档样式"""
        # 设置正文样式
        style = doc.styles['Normal']
        font = style.font
        font.name = '宋体'
        font.size = Pt(12)
        
        # 设置段落格式
        paragraph_format = style.paragraph_format
        paragraph_format.line_spacing = 1.5
        paragraph_format.space_after = Pt(6)

    def _add_basic_info_table(self, doc, lesson_data):
        """添加基本信息表格"""
        doc.add_heading('基本信息', level=1)
        
        table = doc.add_table(rows=4, cols=4)
        table.style = 'Table Grid'
        
        # 填充表格内容
        cells = table.rows[0].cells
        cells[0].text = '学科'
        cells[1].text = lesson_data.get('subject', '')
        cells[2].text = '年级'
        cells[3].text = lesson_data.get('grade', '')
        
        cells = table.rows[1].cells
        cells[0].text = '课题'
        cells[1].text = lesson_data.get('topic', '')
        cells[2].text = '课时'
        cells[3].text = f"{lesson_data.get('duration', 45)}分钟"
        
        cells = table.rows[2].cells
        cells[0].text = '授课教师'
        cells[1].text = ''
        cells[2].text = '授课时间'
        cells[3].text = ''
        
        cells = table.rows[3].cells
        cells[0].text = '授课班级'
        cells[1].text = ''
        cells[2].text = '学生人数'
        cells[3].text = ''

    def _add_teaching_objectives(self, doc, lesson_data):
        """添加教学目标"""
        doc.add_heading('教学目标', level=1)
        
        objectives = lesson_data.get('objectives', [])
        if objectives:
            for i, objective in enumerate(objectives, 1):
                p = doc.add_paragraph(f"{i}. {objective}")
                p.style = 'List Number'
        else:
            doc.add_paragraph('（待完善）')

    def _add_key_points(self, doc, lesson_data):
        """添加重点难点"""
        doc.add_heading('教学重点难点', level=1)
        
        key_points = lesson_data.get('keyPoints', '')
        if key_points:
            doc.add_paragraph(key_points)
        else:
            doc.add_paragraph('（待完善）')

    def _add_teaching_methods(self, doc, lesson_data):
        """添加教学方法"""
        doc.add_heading('教学方法', level=1)
        
        methods = lesson_data.get('methods', [])
        if methods:
            method_text = '、'.join([self._get_method_label(m) for m in methods])
            doc.add_paragraph(method_text)
        else:
            doc.add_paragraph('（待完善）')

    def _add_teaching_materials(self, doc, lesson_data):
        """添加教学准备"""
        doc.add_heading('教学准备', level=1)
        
        materials = lesson_data.get('materials', '')
        if materials:
            doc.add_paragraph(materials)
        else:
            doc.add_paragraph('（待完善）')

    def _add_teaching_process(self, doc, lesson_data):
        """添加教学过程"""
        doc.add_heading('教学过程', level=1)
        
        steps = lesson_data.get('steps', [])
        if steps:
            for i, step in enumerate(steps, 1):
                # 添加步骤标题
                step_heading = doc.add_heading(f"{i}. {step.get('title', '')}（{step.get('time', 0)}分钟）", level=2)
                
                # 添加步骤内容
                content = step.get('content', '')
                if content:
                    doc.add_paragraph(content)
                else:
                    doc.add_paragraph('（待完善）')
        else:
            doc.add_paragraph('（待完善）')

    def _add_homework(self, doc, lesson_data):
        """添加作业布置"""
        doc.add_heading('作业布置', level=1)
        
        homework = lesson_data.get('homework', '')
        if homework:
            doc.add_paragraph(homework)
        else:
            doc.add_paragraph('（待完善）')

    def _add_reflection(self, doc, lesson_data):
        """添加教学反思"""
        doc.add_heading('教学反思', level=1)
        
        reflection = lesson_data.get('reflection', '')
        if reflection:
            doc.add_paragraph(reflection)
        else:
            doc.add_paragraph('（课后补充）')

    def _add_ai_enhanced_content(self, doc, ai_content):
        """添加AI增强内容"""
        doc.add_page_break()
        doc.add_heading('AI智能优化建议', level=1)
        
        # 将AI内容按段落分割并添加
        paragraphs = ai_content.split('\n\n')
        for paragraph in paragraphs:
            if paragraph.strip():
                doc.add_paragraph(paragraph.strip())
