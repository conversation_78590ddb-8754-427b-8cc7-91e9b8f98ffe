# 智能教案生成系统

基于Flask和Vue.js的智能教案生成系统，支持生成Word格式的教案文档。

## 🚀 功能特性

- **智能教案生成**: 基于用户输入生成完整的教案结构
- **Word文档输出**: 生成标准的.docx格式教案文档
- **AI内容增强**: 使用DeepSeek API优化教案内容（可选）
- **简化版服务**: 提供快速响应的基础教案生成功能

## 📁 项目结构

```
flaskProject/
├── api.py                    # Docmee API接口（PPT生成相关）
├── app.py                    # 完整版Flask应用（包含AI增强）
├── simple_app.py             # 简化版Flask应用（推荐使用）
├── config.py                 # 配置文件
├── http_utils.py             # HTTP工具函数
├── requirements.txt          # Python依赖
├── cleanup.py                # 清理脚本
├── services/
│   ├── lesson_plan_service.py # 教案生成服务
│   └── ppt_service.py        # PPT生成服务
├── temp_ppt/                 # 临时文件存储目录
└── templates/                # Flask模板目录（如需要）
```

## 🛠️ 安装和运行

### 1. 安装依赖

```bash
cd flaskProject
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并配置相关API密钥：

```bash
# API密钥配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 应用配置
UPLOAD_FOLDER=temp_ppt
PORT=5300
DEBUG=True
```

### 3. 启动服务

**推荐使用简化版（快速响应）：**
```bash
python simple_app.py
```

**或使用完整版（包含AI增强）：**
```bash
python app.py
```

### 4. 验证服务

访问健康检查端点：
```
http://localhost:5300/health
```

## 📝 API接口

### 生成教案

**POST** `/api/lesson-plan/generate`

请求体示例：
```json
{
  "subject": "数学",
  "grade": "五年级",
  "topic": "分数的基本概念",
  "duration": 45,
  "objectives": [
    "理解分数的基本概念",
    "掌握分数的读写方法"
  ],
  "keyPoints": "分数的概念理解",
  "materials": "教学课件、分数卡片",
  "methods": ["lecture", "demonstration"],
  "steps": [
    {
      "title": "导入新课",
      "content": "通过实物演示引入分数概念",
      "time": 10
    }
  ],
  "homework": "完成课后练习题",
  "reflection": "待课后补充"
}
```

### 下载教案

**GET** `/api/lesson-plan/download/<filename>`

## 🧹 维护

### 清理临时文件

运行清理脚本：
```bash
python cleanup.py
```

该脚本会：
- 清理7天前的临时文件
- 删除Python缓存文件
- 清理日志文件

### 手动清理

删除所有临时文件：
```bash
rm -rf temp_ppt/*
rm -rf __pycache__
rm -rf services/__pycache__
```

## 🔧 配置说明

### 服务版本选择

- **simple_app.py**: 简化版，不依赖AI API，响应快速
- **app.py**: 完整版，包含AI内容增强，需要配置DeepSeek API

### 端口配置

默认端口：5300
可在 `.env` 文件中修改 `PORT` 配置

## 📋 依赖说明

主要依赖：
- Flask: Web框架
- python-docx: Word文档生成
- openai: AI API调用（可选）
- flask-cors: 跨域支持

## 🐛 故障排除

### 常见问题

1. **端口被占用**: 修改 `.env` 中的 `PORT` 配置
2. **依赖缺失**: 运行 `pip install -r requirements.txt`
3. **AI调用超时**: 使用 `simple_app.py` 避免AI依赖

### 日志查看

服务运行时会在控制台输出详细日志，包括：
- 请求处理状态
- 文件生成信息
- 错误详情

## 📄 许可证

本项目仅供学习和研究使用。
