import axios from 'axios'
import { useTokenStore } from '@/stores/token.js'

// 创建axios实例
const request = axios.create({
  baseURL: '/api', // 使用代理路径，避免CORS问题
  timeout: 5000 // 请求超时时间
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    const tokenStore = useTokenStore()
    // 如果有token，添加到请求头
    if (tokenStore.token) {
      config.headers['Authorization'] = tokenStore.token
    }
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    return response.data
  },
  error => {
    // 对响应错误做点什么
    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)

export default request