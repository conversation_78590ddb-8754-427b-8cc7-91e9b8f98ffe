const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
  devServer: {  // 正确的配置位置
    port: 5100, // 设置端口号
    host: 'localhost', // 可选，默认 localhost
    open: true, // 可选，自动打开浏览器
    proxy: {
      '/api': {
        target: 'http://localhost:5300',
        changeOrigin: true,
        ws: true,
        secure: false,
        onProxyReq: (proxyReq, req, res) => {
          // 添加必要的请求头
          proxyReq.setHeader('Accept', 'text/event-stream');
          proxyReq.setHeader('Cache-Control', 'no-cache');
          proxyReq.setHeader('Connection', 'keep-alive');
        }
      }
    }
  }
})