#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import uuid
import logging
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from flask_restful import Api, Resource
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})
api = Api(app)

# 配置
UPLOAD_FOLDER = "temp_ppt"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

class SimpleLessonPlanResource(Resource):
    def post(self):
        try:
            data = request.get_json()
            logger.info(f"收到教案生成请求: {data}")

            # 验证必填字段
            if not data.get('subject') or not data.get('grade') or not data.get('topic'):
                return {
                    "code": 1,
                    "message": "请填写学科、年级和课题"
                }, 400

            # 生成Word文档
            filename = self._create_simple_word_document(data)
            
            logger.info(f"生成教案成功: {filename}")

            # 返回成功响应
            response_data = {
                "code": 0,
                "message": "教案生成成功",
                "data": {
                    "filename": filename,
                    "file_path": os.path.join(UPLOAD_FOLDER, filename)
                }
            }

            return response_data

        except Exception as e:
            logger.error(f"教案生成失败: {str(e)}")
            return {
                "code": 1,
                "message": f"教案生成失败: {str(e)}"
            }, 500

    def _create_simple_word_document(self, lesson_data):
        """创建简单的Word文档"""
        # 生成唯一文件名
        file_id = uuid.uuid4().hex[:8]
        topic = lesson_data.get('topic', '教案')
        filename = f"{topic}_{file_id}.docx"
        doc_path = os.path.join(UPLOAD_FOLDER, filename)

        # 创建文档
        doc = Document()
        
        # 添加标题
        title = doc.add_heading(f"{lesson_data.get('topic', '教案')}", 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加基本信息
        doc.add_heading('基本信息', level=1)
        doc.add_paragraph(f"学科：{lesson_data.get('subject', '')}")
        doc.add_paragraph(f"年级：{lesson_data.get('grade', '')}")
        doc.add_paragraph(f"课题：{lesson_data.get('topic', '')}")
        doc.add_paragraph(f"课时：{lesson_data.get('duration', 45)}分钟")
        
        # 添加教学目标
        doc.add_heading('教学目标', level=1)
        objectives = lesson_data.get('objectives', [])
        if objectives:
            for i, objective in enumerate(objectives, 1):
                doc.add_paragraph(f"{i}. {objective}")
        else:
            doc.add_paragraph('（待完善）')

        # 添加重点难点
        doc.add_heading('教学重点难点', level=1)
        key_points = lesson_data.get('keyPoints', '')
        doc.add_paragraph(key_points if key_points else '（待完善）')

        # 添加教学材料
        doc.add_heading('教学准备', level=1)
        materials = lesson_data.get('materials', '')
        doc.add_paragraph(materials if materials else '（待完善）')

        # 添加教学步骤
        doc.add_heading('教学过程', level=1)
        steps = lesson_data.get('steps', [])
        if steps:
            for i, step in enumerate(steps, 1):
                doc.add_heading(f"{i}. {step.get('title', '')}（{step.get('time', 0)}分钟）", level=2)
                content = step.get('content', '')
                doc.add_paragraph(content if content else '（待完善）')
        else:
            doc.add_paragraph('（待完善）')

        # 添加作业布置
        doc.add_heading('作业布置', level=1)
        homework = lesson_data.get('homework', '')
        doc.add_paragraph(homework if homework else '（待完善）')

        # 添加教学反思
        doc.add_heading('教学反思', level=1)
        reflection = lesson_data.get('reflection', '')
        doc.add_paragraph(reflection if reflection else '（课后补充）')

        # 保存文档
        doc.save(doc_path)
        
        return filename

class LessonPlanDownloadResource(Resource):
    def get(self, filename):
        try:
            # 构建文件路径
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {"error": "文件不存在"}, 404
            
            # 返回文件
            return send_file(
                file_path,
                as_attachment=True,
                download_name=filename
            )
            
        except Exception as e:
            logger.error(f"下载教案文件失败: {str(e)}")
            return {"error": f"下载失败: {str(e)}"}, 500

# 健康检查端点
@app.route('/health')
def health_check():
    return jsonify({"status": "ok", "message": "服务正常运行"})

# 注册路由
api.add_resource(SimpleLessonPlanResource, '/api/lesson-plan/generate')
api.add_resource(LessonPlanDownloadResource, '/api/lesson-plan/download/<string:filename>')

if __name__ == '__main__':
    print("启动简化版教案生成服务...")
    print("服务地址: http://localhost:5300")
    print("健康检查: http://localhost:5300/health")
    app.run(host='0.0.0.0', port=5300, debug=True)
