#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import uuid
import logging
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from flask_restful import Api, Resource
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from openai import OpenAI
from config import config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})
api = Api(app)

# 配置
UPLOAD_FOLDER = "temp_ppt"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 初始化DeepSeek客户端
try:
    deepseek_client = OpenAI(
        api_key=config.DEEPSEEK_API_KEY,
        base_url=config.DEEPSEEK_BASE_URL
    )
    DEEPSEEK_AVAILABLE = True
    logger.info("DeepSeek API客户端初始化成功")
except Exception as e:
    deepseek_client = None
    DEEPSEEK_AVAILABLE = False
    logger.warning(f"DeepSeek API客户端初始化失败: {e}")
    logger.warning("将使用基础模式生成教案")

class SimpleLessonPlanResource(Resource):
    def post(self):
        try:
            data = request.get_json()
            logger.info(f"收到教案生成请求: {data}")

            # 验证必填字段
            if not data.get('subject') or not data.get('grade') or not data.get('topic'):
                return {
                    "code": 1,
                    "message": "请填写学科、年级和课题"
                }, 400

            # 使用DeepSeek API增强教案内容（如果可用）
            enhanced_data = self._enhance_with_deepseek(data)

            # 生成Word文档
            filename = self._create_simple_word_document(enhanced_data)

            logger.info(f"生成教案成功: {filename}")

            # 返回成功响应
            response_data = {
                "code": 0,
                "message": "教案生成成功",
                "data": {
                    "filename": filename,
                    "file_path": os.path.join(UPLOAD_FOLDER, filename),
                    "ai_enhanced": enhanced_data.get('ai_enhanced', False),
                    "ai_error": enhanced_data.get('ai_error', None)
                }
            }

            return response_data

        except Exception as e:
            logger.error(f"教案生成失败: {str(e)}")
            return {
                "code": 1,
                "message": f"教案生成失败: {str(e)}"
            }, 500

    def _enhance_with_deepseek(self, lesson_data):
        """使用DeepSeek API增强教案内容"""
        if not DEEPSEEK_AVAILABLE:
            logger.info("DeepSeek API不可用，使用基础模式")
            lesson_data['ai_enhanced'] = False
            return lesson_data

        try:
            logger.info("开始使用DeepSeek API增强教案内容...")

            # 构建AI提示词
            system_prompt = """你是一个专业的教学设计专家。请根据用户提供的教案基础信息，生成详细、专业的教案内容补充。

请按照以下要求生成内容：

1. **教学目标细化**：将基础目标扩展为知识目标、能力目标、情感目标
2. **教学重点难点分析**：详细分析重点难点及突破方法
3. **教学方法优化**：推荐适合的教学方法和策略
4. **教学过程详化**：为每个教学步骤提供具体的实施方案
5. **板书设计**：设计合理的板书布局
6. **作业设计**：设计分层次的作业内容
7. **教学反思要点**：提供教学反思的关键点

要求：
- 内容要符合教育教学规律
- 适合指定的年级和学科特点
- 语言专业、简洁、实用
- 每部分内容控制在100字以内
"""

            # 构建用户输入
            user_content = f"""
学科：{lesson_data.get('subject', '')}
年级：{lesson_data.get('grade', '')}
课题：{lesson_data.get('topic', '')}
课时：{lesson_data.get('duration', 45)}分钟

教学目标：
{chr(10).join([f"- {obj}" for obj in lesson_data.get('objectives', [])])}

重点难点：{lesson_data.get('keyPoints', '')}
教学材料：{lesson_data.get('materials', '')}
教学步骤：
{chr(10).join([f"{i+1}. {step.get('title', '')}（{step.get('time', 0)}分钟）" for i, step in enumerate(lesson_data.get('steps', []))])}

请为以上教案提供专业的内容补充和优化建议。
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ]

            # 调用DeepSeek API
            response = deepseek_client.chat.completions.create(
                model="deepseek-reasoner",
                messages=messages,
                temperature=0.2,
                max_tokens=2048
            )

            if response.choices and len(response.choices) > 0:
                ai_content = response.choices[0].message.content
                lesson_data['ai_enhanced_content'] = ai_content
                lesson_data['ai_enhanced'] = True
                logger.info("DeepSeek API增强成功")
            else:
                raise ValueError("DeepSeek API返回空内容")

        except Exception as e:
            logger.error(f"DeepSeek API增强失败: {str(e)}")
            lesson_data['ai_enhanced'] = False
            lesson_data['ai_error'] = str(e)

        return lesson_data

    def _create_simple_word_document(self, lesson_data):
        """创建简单的Word文档"""
        # 生成唯一文件名
        file_id = uuid.uuid4().hex[:8]
        topic = lesson_data.get('topic', '教案')
        filename = f"{topic}_{file_id}.docx"
        doc_path = os.path.join(UPLOAD_FOLDER, filename)

        # 创建文档
        doc = Document()
        
        # 添加标题
        title = doc.add_heading(f"{lesson_data.get('topic', '教案')}", 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加基本信息
        doc.add_heading('基本信息', level=1)
        doc.add_paragraph(f"学科：{lesson_data.get('subject', '')}")
        doc.add_paragraph(f"年级：{lesson_data.get('grade', '')}")
        doc.add_paragraph(f"课题：{lesson_data.get('topic', '')}")
        doc.add_paragraph(f"课时：{lesson_data.get('duration', 45)}分钟")
        
        # 添加教学目标
        doc.add_heading('教学目标', level=1)
        objectives = lesson_data.get('objectives', [])
        if objectives:
            for i, objective in enumerate(objectives, 1):
                doc.add_paragraph(f"{i}. {objective}")
        else:
            doc.add_paragraph('（待完善）')

        # 添加重点难点
        doc.add_heading('教学重点难点', level=1)
        key_points = lesson_data.get('keyPoints', '')
        doc.add_paragraph(key_points if key_points else '（待完善）')

        # 添加教学材料
        doc.add_heading('教学准备', level=1)
        materials = lesson_data.get('materials', '')
        doc.add_paragraph(materials if materials else '（待完善）')

        # 添加教学步骤
        doc.add_heading('教学过程', level=1)
        steps = lesson_data.get('steps', [])
        if steps:
            for i, step in enumerate(steps, 1):
                doc.add_heading(f"{i}. {step.get('title', '')}（{step.get('time', 0)}分钟）", level=2)
                content = step.get('content', '')
                doc.add_paragraph(content if content else '（待完善）')
        else:
            doc.add_paragraph('（待完善）')

        # 添加作业布置
        doc.add_heading('作业布置', level=1)
        homework = lesson_data.get('homework', '')
        doc.add_paragraph(homework if homework else '（待完善）')

        # 添加教学反思
        doc.add_heading('教学反思', level=1)
        reflection = lesson_data.get('reflection', '')
        doc.add_paragraph(reflection if reflection else '（课后补充）')

        # 如果有AI增强内容，添加到文档末尾
        if lesson_data.get('ai_enhanced') and lesson_data.get('ai_enhanced_content'):
            doc.add_page_break()
            doc.add_heading('🤖 AI智能优化建议', level=1)

            # 添加AI增强标识
            ai_info = doc.add_paragraph()
            ai_info.add_run("本部分内容由DeepSeek AI生成，提供专业的教学建议和优化方案。").italic = True

            # 将AI内容按段落分割并添加
            ai_content = lesson_data.get('ai_enhanced_content', '')
            paragraphs = ai_content.split('\n\n')
            for paragraph in paragraphs:
                if paragraph.strip():
                    doc.add_paragraph(paragraph.strip())

        elif lesson_data.get('ai_error'):
            # 如果AI增强失败，添加说明
            doc.add_page_break()
            doc.add_heading('AI增强说明', level=1)
            error_info = doc.add_paragraph()
            error_info.add_run(f"AI增强功能暂时不可用：{lesson_data.get('ai_error', '')}").italic = True
            error_info.add_run("\n本教案使用基础模式生成。").italic = True

        # 保存文档
        doc.save(doc_path)

        return filename

class LessonPlanDownloadResource(Resource):
    def get(self, filename):
        try:
            # 构建文件路径
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {"error": "文件不存在"}, 404
            
            # 返回文件
            return send_file(
                file_path,
                as_attachment=True,
                download_name=filename
            )
            
        except Exception as e:
            logger.error(f"下载教案文件失败: {str(e)}")
            return {"error": f"下载失败: {str(e)}"}, 500

# 健康检查端点
@app.route('/health')
def health_check():
    return jsonify({"status": "ok", "message": "服务正常运行"})

# 注册路由
api.add_resource(SimpleLessonPlanResource, '/api/lesson-plan/generate')
api.add_resource(LessonPlanDownloadResource, '/api/lesson-plan/download/<string:filename>')

if __name__ == '__main__':
    print("启动简化版教案生成服务...")
    print("服务地址: http://localhost:5300")
    print("健康检查: http://localhost:5300/health")
    app.run(host='0.0.0.0', port=5300, debug=True)
