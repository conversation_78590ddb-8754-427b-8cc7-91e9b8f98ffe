#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("测试教案生成功能...")

try:
    from services.lesson_plan_service import LessonPlanService
    from config import config
    print("✓ 模块导入成功")
except ImportError as e:
    print(f"✗ 模块导入失败: {e}")
    sys.exit(1)

# 创建测试数据
test_data = {
    "subject": "数学",
    "grade": "五年级",
    "topic": "分数的基本概念",
    "duration": 45,
    "objectives": [
        "理解分数的基本概念",
        "掌握分数的读写方法",
        "能够识别分数的分子和分母"
    ],
    "keyPoints": "分数的概念理解，分子分母的区别",
    "materials": "教学课件、分数卡片、实物教具",
    "methods": ["lecture", "demonstration", "practice"],
    "steps": [
        {
            "title": "导入新课",
            "content": "通过实物演示引入分数概念",
            "time": 10
        },
        {
            "title": "新课讲解",
            "content": "详细讲解分数的定义和组成",
            "time": 20
        },
        {
            "title": "练习巩固",
            "content": "学生练习分数的读写",
            "time": 15
        }
    ],
    "homework": "完成课后练习题1-5",
    "reflection": "待课后补充"
}

print("测试数据准备完成")
print(f"测试数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")

try:
    # 创建教案服务实例
    lesson_service = LessonPlanService(
        api_key=config.DEEPSEEK_API_KEY,
        base_url=config.DEEPSEEK_BASE_URL,
        upload_folder=config.UPLOAD_FOLDER
    )
    print("✓ 教案服务实例创建成功")
    
    # 生成教案
    print("开始生成教案...")
    result = lesson_service.generate_lesson_plan_word(test_data)
    
    print("教案生成结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    if result.get("status") == "success":
        print(f"✓ 教案生成成功！文件路径: {result.get('file_path')}")
        print(f"  文件名: {result.get('filename')}")
    else:
        print(f"✗ 教案生成失败: {result.get('error')}")
        
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()
