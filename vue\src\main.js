import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 导入Pinia
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import axios from 'axios'

const app = createApp(App);

// 创建 Axios 实例
const request = axios.create({
    baseURL: 'http://localhost:5300', // 设置 Flask 后端的地址
    headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
    }
});

// 挂载到 Vue 实例上
app.config.globalProperties.$request = request;

// 创建Pinia实例
const pinia = createPinia();
// 使用持久化插件
pinia.use(piniaPluginPersistedstate);

// 使用Element Plus
app.use(ElementPlus);

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(pinia).use(store).use(router).mount("#app");