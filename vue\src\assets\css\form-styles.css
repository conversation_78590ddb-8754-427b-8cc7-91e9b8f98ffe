.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.half {
  flex: 1;
  margin-bottom: 0;
}

.required {
  color: #e53935;
  margin-left: 2px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

textarea, input, select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.3s, box-shadow 0.3s;
}

textarea:focus, input:focus, select:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

textarea {
  min-height: 120px;
  resize: vertical;
}

select {
  height: 45px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: calc(100% - 12px) center;
  padding-right: 30px;
}

.input-tip {
  margin-top: 6px;
  font-size: 12px;
  color: #666;
}

.form-actions {
  margin-top: 20px;
  text-align: center;
}

.generate-btn {
  padding: 14px 30px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 180px;
  box-shadow: 0 2px 5px rgba(66, 133, 244, 0.2);
}

.generate-btn:hover {
  background-color: #3367d6;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 133, 244, 0.3);
}

.generate-btn:disabled {
  background-color: #a4c2f4;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  margin-top: 20px;
  padding: 12px 15px;
  color: #e53935;
  background-color: rgba(229, 57, 53, 0.1);
  border-radius: 8px;
  font-size: 14px;
  border-left: 3px solid #e53935;
}

.error-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #e53935;
}

.error-text {
  flex: 1;
}
