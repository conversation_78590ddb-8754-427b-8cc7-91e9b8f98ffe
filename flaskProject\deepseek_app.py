#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from flask_restful import Api, Resource
from services.deepseek_lesson_plan_service import DeepseekLessonPlanService
from config import config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})
api = Api(app)

# 配置
UPLOAD_FOLDER = "temp_ppt"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 初始化DeepSeek教案服务
deepseek_lesson_plan_service = DeepseekLessonPlanService(
    api_key=config.DEEPSEEK_API_KEY,
    base_url=config.DEEPSEEK_BASE_URL,
    upload_folder=UPLOAD_FOLDER
)

class DeepseekLessonPlanResource(Resource):
    """DeepSeek智能教案生成资源类"""
    
    def post(self):
        try:
            data = request.get_json()
            logger.info(f"收到DeepSeek教案生成请求: {data}")

            # 验证必填字段
            if not data.get('subject') or not data.get('grade') or not data.get('topic'):
                return {
                    "code": 1,
                    "message": "请填写学科、年级和课题"
                }, 400

            if not data.get('objectives') or len(data.get('objectives', [])) == 0:
                return {
                    "code": 1,
                    "message": "请至少添加一个教学目标"
                }, 400

            # 使用DeepSeek教案服务生成教案
            result = deepseek_lesson_plan_service.generate_lesson_plan(data)

            # 检查是否有错误
            if "error" in result:
                logger.error(f"生成教案失败: {result['error']}")
                return {
                    "code": 1,
                    "message": f"教案生成失败: {result['error']}"
                }, 500

            logger.info(f"生成教案成功: {result}")

            # 返回成功响应，包含文件信息
            response_data = {
                "code": 0,
                "message": "教案生成成功",
                "data": {
                    "filename": result.get("filename", ""),
                    "file_path": result.get("file_path", ""),
                    "ai_generation_success": result.get("enhanced_content", {}).get("ai_generation_success", False),
                    "ai_error": result.get("enhanced_content", {}).get("ai_error", None)
                }
            }

            return response_data

        except Exception as e:
            logger.error(f"教案生成失败: {str(e)}")
            return {
                "code": 1,
                "message": f"教案生成失败: {str(e)}"
            }, 500


class LessonPlanDownloadResource(Resource):
    """教案文件下载资源类"""
    
    def get(self, filename):
        try:
            # 构建文件路径
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {"error": "文件不存在"}, 404
            
            # 返回文件
            return send_file(
                file_path,
                as_attachment=True,
                download_name=filename
            )
            
        except Exception as e:
            logger.error(f"下载教案文件失败: {str(e)}")
            return {"error": f"下载失败: {str(e)}"}, 500


# 健康检查端点
@app.route('/health')
def health_check():
    return jsonify({
        "status": "ok", 
        "message": "DeepSeek教案服务正常运行",
        "deepseek_api_available": bool(config.DEEPSEEK_API_KEY and config.DEEPSEEK_BASE_URL)
    })


# 注册路由
api.add_resource(DeepseekLessonPlanResource, '/api/lesson-plan/generate')
api.add_resource(LessonPlanDownloadResource, '/api/lesson-plan/download/<string:filename>')


if __name__ == '__main__':
    print("=" * 50)
    print("DeepSeek智能教案生成服务")
    print("=" * 50)
    print(f"服务地址: http://localhost:{config.PORT}")
    print(f"健康检查: http://localhost:{config.PORT}/health")
    print(f"DeepSeek API状态: {'可用' if config.DEEPSEEK_API_KEY and config.DEEPSEEK_BASE_URL else '不可用'}")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=config.PORT, debug=True)
