#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import uuid
import logging
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from flask_restful import Api, Resource
from openai import OpenAI
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})
api = Api(app)

# 直接配置DeepSeek API（用于测试）
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_BASE_URL = "https://api.deepseek.com"
UPLOAD_FOLDER = "temp_ppt"

# 创建上传目录
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 初始化DeepSeek客户端
try:
    deepseek_client = OpenAI(
        api_key=DEEPSEEK_API_KEY,
        base_url=DEEPSEEK_BASE_URL
    )
    logger.info("DeepSeek API客户端初始化成功")
except Exception as e:
    logger.error(f"DeepSeek API客户端初始化失败: {e}")
    deepseek_client = None

class TestDeepseekLessonPlanResource(Resource):
    """测试版DeepSeek智能教案生成资源类"""
    
    def post(self):
        try:
            data = request.get_json()
            logger.info(f"收到DeepSeek教案生成请求: {data}")

            # 验证必填字段
            if not data.get('subject') or not data.get('grade') or not data.get('topic'):
                return {
                    "code": 1,
                    "message": "请填写学科、年级和课题"
                }, 400

            # 生成教案
            result = self._generate_lesson_plan(data)
            
            return result

        except Exception as e:
            logger.error(f"教案生成失败: {str(e)}")
            return {
                "code": 1,
                "message": f"教案生成失败: {str(e)}"
            }, 500
    
    def _generate_lesson_plan(self, lesson_data):
        """生成教案"""
        try:
            # 使用DeepSeek API生成内容
            ai_content = self._generate_with_deepseek(lesson_data)
            
            # 创建Word文档
            filename = self._create_word_document(lesson_data, ai_content)
            
            logger.info(f"教案生成成功: {filename}")
            
            return {
                "code": 0,
                "message": "教案生成成功",
                "data": {
                    "filename": filename,
                    "file_path": os.path.join(UPLOAD_FOLDER, filename),
                    "ai_generation_success": ai_content is not None
                }
            }
            
        except Exception as e:
            logger.error(f"生成教案失败: {str(e)}")
            return {
                "code": 1,
                "message": f"生成教案失败: {str(e)}"
            }
    
    def _generate_with_deepseek(self, lesson_data):
        """使用DeepSeek API生成教案内容"""
        if not deepseek_client:
            logger.warning("DeepSeek客户端不可用")
            return None
        
        try:
            logger.info("开始调用DeepSeek API...")
            
            # 构建提示词
            system_prompt = """你是一个专业的教学设计专家。请根据用户提供的教案信息，生成一份完整、专业的教案内容。

请按照以下结构生成教案：

## 一、教学目标
- 知识目标：学生应掌握的具体知识点
- 能力目标：学生应培养的技能和能力  
- 情感目标：学生应形成的态度和价值观

## 二、教学重点与难点
- 教学重点：本课需要重点讲解的内容
- 教学难点：学生理解困难的内容

## 三、教学方法与策略
- 教学方法：具体的教学方法
- 教学策略：教学组织形式

## 四、教学过程
### 1. 导入环节（X分钟）
- 具体的导入方式和内容

### 2. 新课讲授（X分钟）  
- 知识点的讲解方法
- 重难点突破策略

### 3. 巩固练习（X分钟）
- 练习设计和组织

### 4. 总结提升（X分钟）
- 知识总结和能力提升

## 五、板书设计
- 板书的整体布局和重点内容

## 六、作业设计
- 基础作业和拓展作业

## 七、教学反思
- 教学效果预期和改进建议

要求：内容要专业、详细、可操作，适合指定的年级和学科特点。
"""

            user_content = f"""
请为以下课程生成详细教案：

学科：{lesson_data.get('subject', '')}
年级：{lesson_data.get('grade', '')}
课题：{lesson_data.get('topic', '')}
课时：{lesson_data.get('duration', 45)}分钟

教学目标：
{chr(10).join([f"- {obj}" for obj in lesson_data.get('objectives', [])])}

重点难点：{lesson_data.get('keyPoints', '')}
教学材料：{lesson_data.get('materials', '')}
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ]

            # 调用DeepSeek API
            response = deepseek_client.chat.completions.create(
                model="deepseek-reasoner",
                messages=messages,
                temperature=0.2,
                max_tokens=4096
            )

            if response.choices and len(response.choices) > 0:
                ai_content = response.choices[0].message.content
                logger.info(f"DeepSeek API调用成功，生成内容长度: {len(ai_content)}")
                return ai_content
            else:
                logger.error("DeepSeek API返回空内容")
                return None

        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {str(e)}")
            return None
    
    def _create_word_document(self, lesson_data, ai_content):
        """创建Word文档"""
        # 生成文件名
        file_id = uuid.uuid4().hex[:8]
        topic = lesson_data.get('topic', '教案')
        filename = f"{topic}_{file_id}.docx"
        doc_path = os.path.join(UPLOAD_FOLDER, filename)

        # 创建文档
        doc = Document()
        
        # 添加标题
        title = doc.add_heading(f"{topic}", 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加基本信息
        doc.add_heading('基本信息', level=1)
        doc.add_paragraph(f"学科：{lesson_data.get('subject', '')}")
        doc.add_paragraph(f"年级：{lesson_data.get('grade', '')}")
        doc.add_paragraph(f"课题：{lesson_data.get('topic', '')}")
        doc.add_paragraph(f"课时：{lesson_data.get('duration', 45)}分钟")
        
        # 如果有AI生成的内容，添加到文档
        if ai_content:
            doc.add_page_break()
            doc.add_heading('🤖 DeepSeek AI智能生成内容', level=1)
            
            # 分段添加AI内容
            sections = ai_content.split('##')
            for section in sections:
                if section.strip():
                    lines = section.strip().split('\n')
                    if lines:
                        # 第一行作为标题
                        heading = lines[0].strip().lstrip('#').strip()
                        if heading:
                            doc.add_heading(heading, level=2)
                        
                        # 其余行作为内容
                        for line in lines[1:]:
                            line = line.strip()
                            if line:
                                if line.startswith('- '):
                                    doc.add_paragraph(line[2:], style='List Bullet')
                                elif line.startswith('### '):
                                    doc.add_heading(line[4:], level=3)
                                else:
                                    doc.add_paragraph(line)
        else:
            doc.add_paragraph("DeepSeek AI生成失败，使用基础模板。")

        # 保存文档
        doc.save(doc_path)
        return filename


class LessonPlanDownloadResource(Resource):
    """文件下载资源类"""
    
    def get(self, filename):
        try:
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            
            if not os.path.exists(file_path):
                return {"error": "文件不存在"}, 404
            
            return send_file(
                file_path,
                as_attachment=True,
                download_name=filename
            )
            
        except Exception as e:
            logger.error(f"下载文件失败: {str(e)}")
            return {"error": f"下载失败: {str(e)}"}, 500


# 健康检查
@app.route('/health')
def health_check():
    return jsonify({
        "status": "ok", 
        "message": "DeepSeek测试服务正常运行",
        "deepseek_api_available": deepseek_client is not None
    })


# 注册路由
api.add_resource(TestDeepseekLessonPlanResource, '/api/lesson-plan/generate')
api.add_resource(LessonPlanDownloadResource, '/api/lesson-plan/download/<string:filename>')


if __name__ == '__main__':
    print("=" * 50)
    print("DeepSeek智能教案生成测试服务")
    print("=" * 50)
    print(f"服务地址: http://localhost:5300")
    print(f"健康检查: http://localhost:5300/health")
    print(f"DeepSeek API状态: {'可用' if deepseek_client else '不可用'}")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5300, debug=True)
