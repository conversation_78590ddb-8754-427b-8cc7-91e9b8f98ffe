import requests, json
import uuid
import logging
import time
import os
from http_utils import *

# 配置日志
logger = logging.getLogger(__name__)

BASE_URL = 'https://docmee.cn'
OPEN_BASE_URL = 'https://open.docmee.cn'

def create_api_token(api_key: str, uid: str, limit: int=None) -> str:
    """
    创建Docmee API令牌

    Args:
        api_key: Docmee API密钥
        uid: 用户ID（用于数据隔离）
        limit: 令牌有效期限制（小时），None表示默认2小时

    Returns:
        API令牌
    """
    url = BASE_URL + '/api/user/createApiToken'
    body = json.dumps({ 'uid': uid, 'limit': limit,"timeOfHours": 48})
    response = post_json(url, { 'Api-Key': api_key }, body)
    if response.status_code != 200:
        raise RuntimeError('创建apiToken失败，httpStatus=' + str(response.status_code))
    result = json.loads(response.text)
    if result['code'] != 0:
        raise RuntimeError('创建apiToken异常：' + result['message'])
    return result['data']['token']

def random_one_template_id(api_token: str) -> str:
    """获取随机模板ID"""
    url = BASE_URL + '/api/ppt/randomTemplates'
    body = json.dumps({ 'size': 1, 'filters': { 'type': 1 } })
    response = post_json(url, { 'token': api_token }, body)
    if response.status_code != 200:
        raise RuntimeError('获取模板失败，httpStatus=' + str(response.status_code))
    result = json.loads(response.text)
    if result['code'] != 0:
        raise RuntimeError('获取模板异常：' + result['message'])
    return result['data'][0]['id']


def generate_pptx(api_token: str, template_id: str, markdown: str, pptx_property=False) -> any:
    """生成PPT（旧版API）"""
    url = BASE_URL + '/api/ppt/generatePptx'
    body = json.dumps({
        'templateId': template_id,
        'outlineContentMarkdown': markdown,
        'pptxProperty': pptx_property
    })
    response = post_json(url, { 'token': api_token }, body)
    if response.status_code != 200:
        raise RuntimeError('生成PPT失败，httpStatus=' + str(response.status_code))
    result = json.loads(response.text)
    if result['code'] != 0:
        raise RuntimeError('生成PPT异常：' + result['message'])
    return result['data']['pptInfo']


def create_task(api_token: str, markdown: str) -> str:
    """创建PPT生成任务

    Args:
        api_token: Docmee API令牌
        markdown: Markdown格式的PPT内容

    Returns:
        任务ID
    """
    url = OPEN_BASE_URL + '/api/ppt/v2/createTask'

    logger.info(f"创建PPT生成任务，Markdown长度: {len(markdown)}")

    # 准备表单数据
    files = None
    data = {
        'type': '7',  # 7表示Markdown大纲生成
        'content': markdown  # 提供Markdown内容
    }

    # 打印请求参数用于调试
    logger.info(f"创建任务请求参数: type=7, content长度={len(markdown)}")
    logger.info(f"Markdown内容前100个字符: {markdown[:100].replace(chr(10), 'n')}")

    try:
        # 使用multipart/form-data格式发送请求
        headers = {'token': api_token}
        response = requests.post(url, headers=headers, data=data, files=files, verify=False)

        # 记录响应
        logger.info(f"创建任务响应状态码: {response.status_code}")
        logger.info(f"创建任务响应内容: {response.text}")

        if response.status_code != 200:
            logger.error(f"创建任务失败，状态码: {response.status_code}")
            raise RuntimeError(f'创建任务失败，httpStatus={response.status_code}')

        result = json.loads(response.text)

        if result.get('code') != 0:
            error_msg = result.get('message', '未知错误')
            logger.error(f"创建任务异常: {error_msg}")
            raise RuntimeError(f'创建任务异常: {error_msg}')

        # 获取任务ID
        task_id = result.get('data', {}).get('id')
        if not task_id:
            logger.error("创建任务成功但未返回任务ID")
            raise RuntimeError('创建任务成功但未返回任务ID')

        logger.info(f"成功创建任务，任务ID: {task_id}")
        return task_id

    except Exception as e:
        logger.error(f"创建任务时发生错误: {str(e)}")
        raise


def generate_pptx_v2(api_token: str, template_id: str, markdown: str) -> any:
    """生成PPT（新版API）

    Args:
        api_token: Docmee API令牌
        template_id: 模板ID
        markdown: Markdown格式的PPT内容

    Returns:
        包含pptId的字典
    """
    # 首先创建任务获取任务ID
    try:
        task_id = create_task(api_token, markdown)
    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        raise RuntimeError(f'创建任务失败: {str(e)}')

    # 使用任务ID生成PPT
    url = OPEN_BASE_URL + '/api/ppt/v2/generatePptx'

    # 打印请求参数用于调试
    logger.info(f"生成PPT请求参数: templateId={template_id}, taskId={task_id}")

    # 检查markdown内容
    if not markdown or not markdown.strip():
        logger.error("Markdown内容为空，这将导致API错误")
    else:
        logger.info(f"Markdown内容长度: {len(markdown)}")
        logger.info(f"Markdown内容前100个字符: {markdown[:100].replace(chr(10), 'n')}")

    # 构建请求体
    body = json.dumps({
        'id': task_id,
        'templateId': template_id,
        'markdown': markdown  # 确保包含markdown内容
    })

    logger.info(f"调用Docmee API生成PPT，任务ID: {task_id}")

    # 发送请求
    try:
        response = post_json(url, { 'token': api_token }, body)

        # 记录原始响应
        logger.info(f"原始响应: {response.text}")

        if response.status_code != 200:
            logger.error(f"生成PPT失败，状态码: {response.status_code}")
            raise RuntimeError(f'生成PPT失败，httpStatus={response.status_code}')

        result = json.loads(response.text)
        logger.info(f"Docmee API响应: {result}")

        if result.get('code') != 0:
            error_msg = result.get('message', '未知错误')
            logger.error(f"生成PPT异常: {error_msg}")

            # 尝试获取更详细的错误信息
            if 'data' in result:
                logger.error(f"错误详情: {result['data']}")

            raise RuntimeError(f'生成PPT异常: {error_msg}')

        # 返回结果，包含pptId
        ppt_info = result.get('data', {}).get('pptInfo', {})
        ppt_id = ppt_info.get('id', '')

        if not ppt_id:
            logger.warning("API响应中未找到pptId，尝试直接从data中获取")
            # 尝试其他可能的路径
            ppt_id = result.get('data', {}).get('id', '')

        logger.info(f"成功生成PPT，pptId22222222222222222: {ppt_id}")
        logger.info(f"完整响应数1111111231据: {result}")

        return {
            'pptId': ppt_id,
            'taskId': task_id,
            'pptInfo': ppt_info
        }
    except json.JSONDecodeError as e:
        logger.error(f"解析响应JSON失败: {e}")
        logger.error(f"原始响应内容: {response.text if 'response' in locals() else 'No response'}")
        raise RuntimeError(f'解析响应失败: {str(e)}')
    except Exception as e:
        logger.error(f"调用API时发生未知错误: {str(e)}")
        raise