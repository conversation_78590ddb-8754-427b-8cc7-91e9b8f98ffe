#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("开始测试Flask应用...")

try:
    from flask import Flask
    print("✓ Flask导入成功")
except ImportError as e:
    print(f"✗ Flask导入失败: {e}")
    sys.exit(1)

try:
    from services.lesson_plan_service import LessonPlanService
    print("✓ LessonPlanService导入成功")
except ImportError as e:
    print(f"✗ LessonPlanService导入失败: {e}")
    sys.exit(1)

try:
    from config import config
    print("✓ Config导入成功")
    print(f"  - DEEPSEEK_API_KEY: {'已设置' if config.DEEPSEEK_API_KEY else '未设置'}")
    print(f"  - UPLOAD_FOLDER: {config.UPLOAD_FOLDER}")
    print(f"  - PORT: {config.PORT}")
except ImportError as e:
    print(f"✗ Config导入失败: {e}")
    sys.exit(1)

try:
    import app
    print("✓ App模块导入成功")
except ImportError as e:
    print(f"✗ App模块导入失败: {e}")
    sys.exit(1)

print("\n所有模块导入成功！现在启动Flask应用...")

if __name__ == '__main__':
    try:
        print(f"启动Flask应用，端口: {config.PORT}")
        app.app.run(host='0.0.0.0', port=config.PORT, debug=True)
    except Exception as e:
        print(f"启动Flask应用失败: {e}")
        sys.exit(1)
