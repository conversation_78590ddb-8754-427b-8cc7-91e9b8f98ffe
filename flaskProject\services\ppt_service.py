import os
import uuid
import logging
import json
from openai import OpenAI
from typing import Optional, List, Dict, Any
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from api import *
from config import config

# 配置日志
logger = logging.getLogger(__name__)

class PPTService:
    def __init__(self, api_key: str, base_url: str, upload_folder: str):
        """
        初始化PPT服务
        :param api_key: API密钥
        :param base_url: API基础URL
        :param upload_folder: PPT文件上传目录
        """
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        self.upload_folder = upload_folder
        os.makedirs(upload_folder, exist_ok=True)

        # 定义配色方案
        self.COLORS = {
            'primary': RGBColor(41, 128, 185),    # 主色调：蓝色
            'secondary': RGBColor(44, 62, 80),    # 次要色：深灰
            'accent': RGBColor(231, 76, 60),      # 强调色：红色
            'light': RGBColor(236, 240, 241),     # 浅色：近白
            'dark': RGBColor(52, 73, 94)          # 深色：深灰蓝
        }


    def generate_Advanced_ppt(self, content: str, title: str, slide_count: int) -> Dict[str, Any]:
        """
        生成高级版PPT

        Args:
            content: PPT内容
            title: PPT标题
            slide_count: PPT页数

        Returns:
            包含pptId的字典或错误信息
        """
        try:
            # 检查是否配置了Docmee API密钥
            if not config.DOCMEE_API_KEY:
                logger.error("未配置Docmee API密钥")
                return {
                    "error": "未配置Docmee API密钥，请在.env文件中设置DOCMEE_API_KEY",
                    "status": "error"
                }

            # 创建API令牌
            try:
                api_token = create_api_token(
                    api_key=config.DOCMEE_API_KEY,
                    uid=config.DOCMEE_USER_ID
                )
                logger.info(f"成功创建Docmee API令牌: {api_token}")
 
            except Exception as e:
                logger.error(f"创建Docmee API令牌失败: {str(e)}")
                return {
                    "error": f"创建Docmee API令牌失败: {str(e)}",
                    "status": "error"
                }

            # 生成PPT内容（Markdown格式）
            markdown_content = self._generate_Advanced_content(content, title, slide_count)
            logger.info(f"生成的Markdown内容长度: {len(markdown_content)}")
            print(markdown_content)

            # 获取随机模板ID
            try:
                template_id = random_one_template_id(api_token)
                logger.info(f"获取到模板ID: {template_id}")
            except Exception as e:
                logger.error(f"获取模板ID失败: {str(e)}")
                return {
                    "error": f"获取模板ID失败: {str(e)}",
                    "status": "error"
                }

            # 调用Docmee API生成PPT
            try:
                result = generate_pptx_v2(
                    api_token=api_token,
                    template_id=template_id,
                    markdown=markdown_content
                )
                logger.info(f"生成PPT成功，pptId: {result.get('pptId')}")

                # 返回结果
                return {
                    "pptId": result.get('pptId'),
                    "taskId": result.get('taskId'),
                    "status": "success"
                }
            except Exception as e:
                logger.error(f"调用Docmee API失败: {str(e)}")
                return {
                    "error": f"调用Docmee API失败: {str(e)}",
                    "status": "error"
                }

        except Exception as e:
            logger.error(f"生成高级版PPT失败: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "status": "error"
            }

    def _generate_Advanced_content(self, content: str, title: str, slide_count: int) -> dict:

        system_prompt = f"""你是一个专业PPT内容生成助手。请生成{slide_count}页PPT内容,每一页markdown都要给出：
        请严格遵循以下Markdown格式规范生成内容：

            # 主题（必须且唯一的一级标题）
            ## 章节标题（必须包含约6个二级章节）
            ### 页面标题（每个章节下约3个三级标题，30字内）
            #### 段落标题（每个页面下约3个四级标题，30字内）
            - 段落文本（40-80字）
            - 带图片的文本内容
            ![配图描述](图片URL)

            表格要求：
            | 表头1    | 表头2    |
            | -------- | -------- |
            | 数据单元格 | 数据单元格 |

            规范要求：
            1. 必须包含且只有一个一级标题
            2. 二级章节标题控制在6个左右
            3. 每个章节下3个左右页面标题
            4. 每个页面包含3个左右段落
            5. 段落内容使用无序列表形式
            6. 每段文字后最多配一张图片
            7. 表格需要完整呈现
            8. 所有标题层级严格使用#符号标记
            9. 避免使用其他格式元素（如加粗、斜体等）
                    """
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"主题：{title}\n内容描述：{content}"}
        ]

        try:
            response = self.client.chat.completions.create(
                model="deepseek-reasoner",
                messages=messages,
                temperature=0.2,
                max_tokens=4096
            )
            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"生成PPTMarkdown失败: {str(e)}", exc_info=True)
            raise


    def generate_Simple_ppt(self, content: str, title: str, slide_count: int, template_style: str, color_theme: str, font_family: str) -> Dict[str, Any]:
        try:
            # 生成PPT内容
            ppt_content = self._generate_content(
                content, title, slide_count,
                template_style, color_theme, font_family
            )
            print(ppt_content)
            # 转换为PPTX
            pptx_path = self._create_pptx(ppt_content, title)
            logger.info(f"PPT生成成功：{pptx_path}")

            # 返回包含文件路径和文件名的字典
            filename = os.path.basename(pptx_path)
            return {
                "file_path": pptx_path,
                "filename": filename,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"生成PPT失败: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "status": "error"
            }

    def _generate_content(self, content: str, title: str, slide_count: int, template_style: str, color_theme: str, font_family: str) -> dict:
        """
        使用Deepseek生成PPT内容
        """
        system_prompt = f"""你是一个专业PPT内容生成助手。请生成{slide_count}页PPT内容，每页内容需要包含以下信息：

                    1. 第一页（标题页）：
                    - title: 主标题
                    - subtitle: 副标题
                    - description: 简要介绍PPT的主题和目的

                    2. 第二页（目录页）：
                    - title: "目录"
                    - sections: 列出所有章节标题的列表

                    3. 后续内容页：
                    每页需要包含：
                    - title: 页面标题
                    - type: 页面类型（可选值：content, two_column, image_content）
                    - points: 主要内容点列表，每个点必须是一个包含main和details的对象:
                    {{
                        "main": "主要观点",
                        "details": ["详细说明1", "详细说明2"]
                    }}
                    - notes: 演讲者注释（可选）

                    4. 最后一页（结束页）：
                    - title: "总结与问答"
                    - points: 总结要点列表（使用与内容页相同的point格式）

                    请特别注意：
                    - points字段中的每个元素必须是对象，不能是纯字符串
                    - 确保所有字符串使用双引号
                    - 不要包含注释或其他非JSON内容
                    - 段落文本（40-80字）
                    - 每个页面包含3个左右段落
                    - 段落内容使用无序列表形式
                    - 每段文字后最多配一张图片
                    - 表格需要完整呈现
                    - 所有标题层级严格使用#符号标记
                    - 避免使用其他格式元素（如加粗、斜体等）

                    请以严格JSON格式返回，确保格式如下：
                    {{
                        "slides": [
                            {{
                                "title": "主标题",
                                "subtitle": "副标题",
                                "description": "简介"
                            }},
                            {{
                                "title": "目录",
                                "sections": ["章节1", "章节2"]
                            }},
                            {{
                                "title": "章节标题",
                                "type": "content",
                                "points": [
                                    {{
                                        "main": "主要观点",
                                        "details": ["详细说明1", "详细说明2"]
                                    }}
                                ],
                                "notes": "演讲者注释"
                            }}
                        ]
                    }}
                    """
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"标题：{title}\n内容：{content}"}
        ]

        try:
            response = self.client.chat.completions.create(
                model="deepseek-reasoner",
                messages=messages,
                temperature=0.2,
                max_tokens=4096
            )

            if not response.choices or len(response.choices) == 0:
                raise ValueError("API响应中没有生成内容")

            generated_content = response.choices[0].message.content
            if not generated_content:
                raise ValueError("API生成的内容为空")

            # 清理和解析JSON内容
            try:
                content = generated_content.strip()
                if content.startswith("```json"):
                    content = content[7:]
                elif content.startswith("```"):
                    content = content[3:]
                if content.endswith("```"):
                    content = content[:-3]

                content = content.strip()
                logger.info(f"清理后的内容: {content}")

                ppt_content = json.loads(content)

                # 验证内容结构
                if not isinstance(ppt_content, dict) or 'slides' not in ppt_content:
                    raise ValueError("生成的内容缺少必要的'slides'字段")

                if not isinstance(ppt_content['slides'], list):
                    raise ValueError("'slides'字段必须是列表类型")

                # 验证points结构
                for slide in ppt_content['slides']:
                    if 'points' in slide:
                        for point in slide['points']:
                            if not isinstance(point, dict) or 'main' not in point:
                                raise ValueError("points中的元素必须包含'main'字段的字典")

                return ppt_content

            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}")
                logger.error(f"原始内容: {generated_content}")
                raise ValueError(f"生成的内容不是有效的JSON格式: {str(e)}")

        except Exception as e:
            logger.error(f"生成PPT内容失败: {str(e)}", exc_info=True)
            raise

    def _create_pptx(self, ppt_content: dict, title: str) -> str:
        """
        使用python-pptx创建PPT文件
        """
        file_id = uuid.uuid4().hex
        base_name = f"{title}_{file_id}"
        pptx_path = os.path.join(self.upload_folder, f"{base_name}.pptx")

        try:
            prs = Presentation()
            prs.slide_width = Inches(16)
            prs.slide_height = Inches(9)

            for slide_data in ppt_content.get('slides', []):
                if slide_data.get('type') == 'two_column':
                    slide = prs.slides.add_slide(prs.slide_layouts[6])
                    title_shape = slide.shapes.add_textbox(
                        Inches(1), Inches(0.5), Inches(14), Inches(1)
                    )
                else:
                    slide = prs.slides.add_slide(prs.slide_layouts[1])
                    title_shape = slide.shapes.title

                if title_shape:
                    title_shape.text = slide_data.get('title', '')
                    title_frame = title_shape.text_frame
                    title_paragraph = title_frame.paragraphs[0]
                    title_paragraph.alignment = PP_ALIGN.CENTER
                    title_run = title_paragraph.runs[0]
                    title_run.font.size = Pt(44)
                    title_run.font.bold = True
                    title_run.font.color.rgb = self.COLORS['primary']

                if 'subtitle' in slide_data:
                    if len(slide.placeholders) > 1:
                        subtitle = slide.placeholders[1]
                        subtitle.text = slide_data['subtitle']
                        subtitle_frame = subtitle.text_frame
                        subtitle_paragraph = subtitle_frame.paragraphs[0]
                        subtitle_paragraph.alignment = PP_ALIGN.CENTER
                        subtitle_run = subtitle_paragraph.runs[0]
                        subtitle_run.font.size = Pt(24)
                        subtitle_run.font.italic = True
                        subtitle_run.font.color.rgb = self.COLORS['secondary']

                elif 'sections' in slide_data:
                    if len(slide.placeholders) > 1:
                        content = slide.placeholders[1]
                        tf = content.text_frame
                        for section in slide_data['sections']:
                            p = tf.add_paragraph()
                            p.text = f"• {section}"
                            p.font.size = Pt(24)
                            p.font.color.rgb = self.COLORS['secondary']
                            p.space_after = Pt(12)

                elif 'points' in slide_data:
                    if slide_data.get('type') == 'two_column':
                        left_content = slide.shapes.add_textbox(
                            Inches(1), Inches(2), Inches(6.5), Inches(5)
                        )
                        right_content = slide.shapes.add_textbox(
                            Inches(8.5), Inches(2), Inches(6.5), Inches(5)
                        )
                        mid_point = len(slide_data['points']) // 2
                        left_points = slide_data['points'][:mid_point]
                        right_points = slide_data['points'][mid_point:]
                        self._add_points_to_textbox(left_content.text_frame, left_points)
                        self._add_points_to_textbox(right_content.text_frame, right_points)
                    else:
                        if len(slide.placeholders) > 1:
                            content = slide.placeholders[1]
                            self._add_points_to_textbox(content.text_frame, slide_data['points'])

                if 'notes' in slide_data:
                    notes_slide = slide.notes_slide
                    notes_slide.notes_text_frame.text = slide_data['notes']

            prs.save(pptx_path)
            logger.info(f"PPT文件已保存到：{pptx_path}")
            return pptx_path

        except Exception as e:
            logger.error(f"生成PPT文件失败：{str(e)}", exc_info=True)
            raise

    def _add_points_to_textbox(self, text_frame, points: List[Dict[str, Any]]) -> None:
        """向文本框添加要点内容（增强容错处理）"""
        for raw_point in points:
            # 容错处理：如果point是字符串，转换为字典
            if isinstance(raw_point, str):
                point = {'main': raw_point, 'details': []}
            else:
                point = raw_point

            # 添加主要观点
            p = text_frame.add_paragraph()
            p.text = f"• {point.get('main', '')}"
            p.font.size = Pt(28)
            p.font.bold = True
            p.font.color.rgb = self.COLORS['primary']
            p.space_after = Pt(12)

            # 添加详细说明
            for detail in point.get('details', []):
                p = text_frame.add_paragraph()
                p.text = f"  - {detail}"
                p.font.size = Pt(20)
                p.font.color.rgb = self.COLORS['secondary']
                p.space_after = Pt(8)