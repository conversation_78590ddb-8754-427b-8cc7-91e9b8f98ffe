#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import json
import requests
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🤖 测试DeepSeek智能教案生成功能...")

# 测试数据
test_data = {
    "subject": "语文",
    "grade": "五年级",
    "topic": "《桂花雨》课文分析",
    "duration": 45,
    "objectives": [
        "理解课文内容，感受作者对家乡桂花的深厚感情",
        "学习作者通过具体事物表达情感的写作方法",
        "培养学生热爱家乡、珍惜美好回忆的情感",
        "掌握课文中的重点词语和句子的含义"
    ],
    "keyPoints": "理解课文表达的思想感情，学习作者的表达方法",
    "materials": "多媒体课件、桂花实物或图片、音频朗读材料、相关视频",
    "methods": ["lecture", "discussion", "multimedia", "group_work"],
    "steps": [
        {
            "title": "情境导入",
            "content": "播放桂花飘香的视频，让学生感受桂花的美丽",
            "time": 8
        },
        {
            "title": "初读课文",
            "content": "学生自由朗读课文，整体感知课文内容",
            "time": 10
        },
        {
            "title": "深入分析",
            "content": "分段分析课文，理解重点词句，体会作者情感",
            "time": 22
        },
        {
            "title": "总结升华",
            "content": "总结课文主题，引导学生表达对家乡的情感",
            "time": 5
        }
    ],
    "homework": "1. 背诵课文第3段；2. 写一篇关于家乡某种植物的小作文；3. 收集描写植物的优美词句",
    "reflection": "观察学生的情感体验和表达能力的提升"
}

def test_deepseek_service():
    """测试DeepSeek教案生成服务"""
    print("\n" + "="*60)
    print("🚀 测试DeepSeek智能教案生成服务")
    print("="*60)
    
    try:
        # 测试健康检查
        print("1. 检查服务状态...")
        health_response = requests.get('http://localhost:5300/health', timeout=5)
        print(f"   健康检查状态: {health_response.status_code}")
        
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"   服务状态: {health_data.get('status')}")
            print(f"   DeepSeek API: {'可用' if health_data.get('deepseek_api_available') else '不可用'}")
            
            if not health_data.get('deepseek_api_available'):
                print("   ⚠️  DeepSeek API不可用，请检查配置")
        else:
            print("   ❌ 服务未运行，请先启动 deepseek_app.py")
            return False
        
        print("\n2. 发送教案生成请求...")
        print(f"   课题: {test_data['subject']} - {test_data['topic']}")
        print(f"   年级: {test_data['grade']}")
        print(f"   教学目标数量: {len(test_data['objectives'])}")
        
        # 测试教案生成
        url = "http://localhost:5300/api/lesson-plan/generate"
        headers = {"Content-Type": "application/json"}
        
        start_time = time.time()
        print(f"   发送请求到: {url}")
        print("   ⏳ 正在生成教案，请稍候...")
        
        response = requests.post(url, json=test_data, headers=headers, timeout=120)  # 增加超时时间
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"   响应状态码: {response.status_code}")
        print(f"   响应时间: {duration:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print("\n3. 分析响应结果...")
            print(f"   API调用: ✅ 成功")
            
            if result.get("code") == 0:
                data = result.get("data", {})
                filename = data.get("filename")
                ai_success = data.get("ai_generation_success", False)
                ai_error = data.get("ai_error")
                
                print(f"   教案生成: ✅ 成功")
                print(f"   文件名: {filename}")
                print(f"   DeepSeek AI增强: {'✅ 成功' if ai_success else '❌ 失败'}")
                
                if not ai_success and ai_error:
                    print(f"   AI错误信息: {ai_error}")
                
                if filename:
                    print("\n4. 测试文件下载...")
                    # 测试文件下载
                    download_url = f"http://localhost:5300/api/lesson-plan/download/{filename}"
                    print(f"   下载URL: {download_url}")
                    
                    download_response = requests.get(download_url, timeout=10)
                    print(f"   下载状态码: {download_response.status_code}")
                    
                    if download_response.status_code == 200:
                        print(f"   文件大小: {len(download_response.content)} 字节")
                        
                        # 保存文件到本地进行验证
                        local_filename = f"test_deepseek_{filename}"
                        with open(local_filename, "wb") as f:
                            f.write(download_response.content)
                        print(f"   ✅ 文件已保存为: {local_filename}")
                        
                        print("\n5. 生成结果总结...")
                        print("   ✅ DeepSeek智能教案生成测试完全成功！")
                        print("   📄 生成的教案包含:")
                        print("      - 基本信息和教学目标")
                        if ai_success:
                            print("      - DeepSeek AI智能生成的详细教案内容")
                            print("      - 专业的教学设计和建议")
                        else:
                            print("      - 基础教案模板内容")
                        print("      - 完整的Word文档格式")
                        
                        return True
                    else:
                        print(f"   ❌ 文件下载失败: {download_response.text}")
                        return False
                else:
                    print("   ❌ 响应中没有文件名")
                    return False
            else:
                print(f"   ❌ API返回错误: {result.get('message')}")
                return False
        else:
            print(f"   ❌ API调用失败，状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectRefused:
        print("   ❌ 无法连接到服务")
        print("   请确保 deepseek_app.py 正在运行")
        return False
    except requests.exceptions.Timeout:
        print("   ❌ 请求超时")
        print("   DeepSeek API可能响应较慢，请稍后重试")
        return False
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎓 DeepSeek智能教案生成系统测试")
    print("参考PPT生成功能，使用DeepSeek API生成智能教案")
    
    # 显示测试数据
    print(f"\n📋 测试数据预览:")
    print(f"学科: {test_data['subject']}")
    print(f"年级: {test_data['grade']}")
    print(f"课题: {test_data['topic']}")
    print(f"教学目标: {len(test_data['objectives'])}个")
    print(f"教学步骤: {len(test_data['steps'])}个")
    
    # 执行测试
    success = test_deepseek_service()
    
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    if success:
        print("🎉 DeepSeek智能教案生成测试成功！")
        print("\n✨ 功能特点:")
        print("   - 使用DeepSeek API智能生成教案内容")
        print("   - 参考PPT生成功能的实现方式")
        print("   - 生成专业、详细的教案结构")
        print("   - 支持Word文档格式输出")
        print("   - 包含AI增强的教学建议")
        
        print("\n🔧 使用方法:")
        print("   1. 启动服务: python deepseek_app.py")
        print("   2. 在Vue前端使用教案生成功能")
        print("   3. 系统会自动调用DeepSeek API生成智能内容")
        
    else:
        print("❌ DeepSeek智能教案生成测试失败")
        print("\n🔧 故障排除:")
        print("   1. 检查 deepseek_app.py 是否正在运行")
        print("   2. 检查 .env 文件中的 DEEPSEEK_API_KEY 配置")
        print("   3. 检查网络连接和API访问权限")
        print("   4. 查看服务日志获取详细错误信息")

if __name__ == "__main__":
    main()
