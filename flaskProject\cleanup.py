#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
清理脚本 - 用于清理临时文件和缓存
"""

import os
import shutil
import glob
from datetime import datetime, timedelta

def cleanup_temp_files(keep_days=7):
    """
    清理临时文件，保留最近几天的文件
    
    Args:
        keep_days: 保留最近几天的文件，默认7天
    """
    temp_folder = "temp_ppt"
    if not os.path.exists(temp_folder):
        print(f"临时文件夹 {temp_folder} 不存在")
        return
    
    cutoff_time = datetime.now() - timedelta(days=keep_days)
    deleted_count = 0
    
    print(f"开始清理 {temp_folder} 文件夹...")
    print(f"将删除 {keep_days} 天前的文件...")
    
    for filename in os.listdir(temp_folder):
        file_path = os.path.join(temp_folder, filename)
        
        if os.path.isfile(file_path):
            # 获取文件修改时间
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            if file_time < cutoff_time:
                try:
                    os.remove(file_path)
                    print(f"已删除: {filename}")
                    deleted_count += 1
                except Exception as e:
                    print(f"删除失败 {filename}: {e}")
    
    print(f"清理完成，共删除 {deleted_count} 个文件")

def cleanup_cache():
    """清理Python缓存文件"""
    print("开始清理Python缓存文件...")
    
    cache_patterns = [
        "__pycache__",
        "*.pyc",
        "*.pyo",
        "*.pyd",
        ".pytest_cache",
        "*.egg-info"
    ]
    
    deleted_count = 0
    
    for pattern in cache_patterns:
        if pattern == "__pycache__":
            # 查找所有 __pycache__ 目录
            for root, dirs, files in os.walk("."):
                for dir_name in dirs:
                    if dir_name == "__pycache__":
                        cache_path = os.path.join(root, dir_name)
                        try:
                            shutil.rmtree(cache_path)
                            print(f"已删除缓存目录: {cache_path}")
                            deleted_count += 1
                        except Exception as e:
                            print(f"删除缓存目录失败 {cache_path}: {e}")
        else:
            # 查找匹配的文件
            for file_path in glob.glob(f"**/{pattern}", recursive=True):
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        print(f"已删除缓存文件: {file_path}")
                        deleted_count += 1
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                        print(f"已删除缓存目录: {file_path}")
                        deleted_count += 1
                except Exception as e:
                    print(f"删除失败 {file_path}: {e}")
    
    print(f"缓存清理完成，共删除 {deleted_count} 个项目")

def cleanup_logs():
    """清理日志文件"""
    print("开始清理日志文件...")
    
    log_patterns = [
        "*.log",
        "logs/*.log",
        "*.log.*"
    ]
    
    deleted_count = 0
    
    for pattern in log_patterns:
        for file_path in glob.glob(pattern, recursive=True):
            try:
                os.remove(file_path)
                print(f"已删除日志文件: {file_path}")
                deleted_count += 1
            except Exception as e:
                print(f"删除日志文件失败 {file_path}: {e}")
    
    print(f"日志清理完成，共删除 {deleted_count} 个文件")

def main():
    """主函数"""
    print("=" * 50)
    print("开始清理项目文件...")
    print("=" * 50)
    
    # 清理临时文件（保留7天内的）
    cleanup_temp_files(keep_days=7)
    print()
    
    # 清理缓存文件
    cleanup_cache()
    print()
    
    # 清理日志文件
    cleanup_logs()
    print()
    
    print("=" * 50)
    print("清理完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
